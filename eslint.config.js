import { defineConfig, globalIgnores } from "eslint/config";
import js from "@eslint/js";
import globals from "globals";
import tseslint from "typescript-eslint";
import pluginVue from "eslint-plugin-vue";
import tsParser from "@typescript-eslint/parser";

export default defineConfig([
  globalIgnores(["./dist"]),
  { files: ["**/*.{js,mjs,cjs,ts,vue}"], plugins: { js }, extends: ["js/recommended"] },
  { files: ["**/*.{js,mjs,cjs,ts,vue}"], languageOptions: { globals: globals.browser } },
  tseslint.configs.recommended,
  pluginVue.configs["flat/essential"],
  { files: ["**/*.vue"], languageOptions: { parserOptions: { parser: tseslint.parser } } },
  {
    plugins: [tsParser()]
  },
  {
    rules: {
      "no-var": "error",
      "no-explicit-any": "off",
      "@typescript-eslint/no-explicit-any": "off",
      "vue/multi-word-component-names": "off",
      "ban-ts-comment": "off",
      "@typescript-eslint/ban-ts-comment": "off",
      "no-unused-vars": [
        "error",
        {
          args: "after-used",
          argsIgnorePattern: "_"
        }
      ],
      "@typescript-eslint/no-unused-vars": [
        "error",
        {
          args: "after-used",
          argsIgnorePattern: "_"
        }
      ]
    }
  }
]);
