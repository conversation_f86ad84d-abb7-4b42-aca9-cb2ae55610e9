# ROS地图组件使用说明

## 概述

ROS地图组件 (`RealTimeLocation.vue`) 是一个用于显示ROS机器人地图和实时位置的Vue组件。它通过WebSocket连接到ROS Bridge服务器，订阅地图数据和机器人位置信息，并在Web界面中实时显示。

## 功能特性

- ✅ 实时显示ROS地图数据
- ✅ 通过父组件props显示机器人位置和姿态（红色圆点+白色箭头）
- ✅ 支持地图缩放和拖拽操作
- ✅ 自动重连机制
- ✅ 连接状态指示
- ✅ 全屏显示支持
- ✅ 响应式设计
- ✅ 实时位置坐标和朝向显示
- ✅ 位置数据响应式更新

## 前置条件

### 1. ROS环境要求

确保您的ROS环境中运行了以下节点：

```bash
# ROS Bridge服务器
roslaunch rosbridge_server rosbridge_websocket.launch

# 地图服务器（如果使用静态地图）
rosrun map_server map_server your_map.yaml

# 或者SLAM算法（如果使用动态建图）
roslaunch gmapping slam_gmapping.launch

# 机器人定位（里程计或AMCL）
roslaunch robot_localization ekf_template.launch
```

### 2. 话题要求

组件会订阅以下ROS话题：

- `/map` - 地图数据 (nav_msgs/OccupancyGrid)
- `/odom` - 机器人位置 (nav_msgs/Odometry)
- `/scan` - 激光雷达数据 (sensor_msgs/LaserScan) [可选]
- `/move_base/NavfnROS/plan` - 路径规划 (nav_msgs/Path) [可选]

## 配置说明

### 1. 修改ROS连接配置

编辑 `src/config/ros.ts` 文件：

```typescript
export const defaultRosConfig: RosConfig = {
  // 修改为您的ROS Bridge服务器地址
  url: "ws://YOUR_ROS_BRIDGE_IP:9090",
  
  reconnect: {
    enabled: true,
    interval: 3000,
    maxAttempts: 5
  },
  
  topics: {
    map: "/map",
    robotPose: "/odom",  // 或 "/amcl_pose"
    laserScan: "/scan",
    path: "/move_base/NavfnROS/plan",
    goalPose: "/move_base_simple/goal"
  },
  
  map: {
    defaultWidth: 800,
    defaultHeight: 600,
    minScale: 0.1,
    maxScale: 5.0,
    scaleStep: 0.1
  }
};
```

### 2. 环境配置

根据不同环境使用不同配置：

```typescript
// 开发环境
export const devRosConfig: RosConfig = {
  ...defaultRosConfig,
  url: "ws://localhost:9090"
};

// 生产环境  
export const prodRosConfig: RosConfig = {
  ...defaultRosConfig,
  url: "ws://*************:9090"
};
```

## 使用方法

### 1. 基本使用

在Vue组件中使用：

```vue
<template>
  <div class="map-container">
    <RealTimeLocation
      :display-mode="displayMode"
      :pos-x="robotPose.posX"
      :pos-y="robotPose.posY"
      :orientation="robotPose.orientation"
      @full-screen="handleFullScreen"
      @exit-full-screen="handleExitFullScreen"
    />
  </div>
</template>

<script setup>
import RealTimeLocation from '@/views/home/<USER>/RealTimeLocation.vue';

const displayMode = ref('default');

// 机器人位置数据（通常来自WebSocket或API）
const robotPose = ref({
  posX: "1.5",    // X坐标（字符串格式）
  posY: "2.0",    // Y坐标（字符串格式）
  orientation: "45" // 朝向角度（字符串格式，单位：度）
});

const handleFullScreen = (componentName, props) => {
  displayMode.value = 'fullScreen';
};

const handleExitFullScreen = () => {
  displayMode.value = 'default';
};
</script>
```

### 2. 组件属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| displayMode | 'default' \| 'fullScreen' | 'default' | 显示模式 |
| posX | string | "" | 机器人X坐标（米） |
| posY | string | "" | 机器人Y坐标（米） |
| orientation | string | "" | 机器人朝向角度（度） |

### 3. 组件事件

| 事件 | 参数 | 说明 |
|------|------|------|
| fullScreen | (componentName: string, props: object) | 进入全屏模式 |
| exitFullScreen | () | 退出全屏模式 |

### 4. 组件方法

通过ref可以调用以下方法：

```vue
<template>
  <RealTimeLocation ref="mapRef" />
</template>

<script setup>
const mapRef = ref();

// 重置地图视图
const resetMap = () => {
  mapRef.value?.resetMapView();
};

// 重新连接ROS
const reconnect = () => {
  mapRef.value?.reconnectRos();
};
</script>
```

## 交互操作

### 鼠标操作

- **拖拽**: 按住鼠标左键拖拽可以移动地图
- **滚轮**: 滚动鼠标滚轮可以缩放地图
- **重置**: 点击重置按钮恢复地图到初始视图

### 键盘快捷键

- `R`: 重置地图视图
- `F`: 切换全屏模式

## 故障排除

### 1. 连接失败

**问题**: 显示"未连接"状态

**解决方案**:
- 检查ROS Bridge服务器是否运行
- 确认WebSocket地址和端口正确
- 检查网络连接和防火墙设置

### 2. 地图不显示

**问题**: 连接成功但地图不显示

**解决方案**:
- 确认`/map`话题有数据发布: `rostopic echo /map`
- 检查地图数据格式是否正确
- 查看浏览器控制台错误信息

### 3. 机器人位置不显示

**问题**: 地图显示正常但机器人位置不显示

**解决方案**:
- 确认`/odom`话题有数据发布: `rostopic echo /odom`
- 检查坐标系是否正确
- 确认位置数据在地图范围内
- 使用测试页面的"测试机器人位置"按钮验证功能
- 检查浏览器控制台是否有位置数据日志

### 4. 机器人位置标记说明

**视觉元素**:
- **红色圆点**: 表示机器人当前位置
- **白色箭头**: 表示机器人朝向
- **位置坐标**: 显示在标题栏中，格式为 (x, y)

**坐标系统**:
- 使用ROS标准坐标系（右手坐标系）
- X轴向前，Y轴向左
- 位置单位为米

## 开发调试

### 1. 启用调试模式

在浏览器控制台中设置：

```javascript
// 启用ROS调试日志
localStorage.setItem('ros-debug', 'true');
```

### 2. 查看ROS消息

```bash
# 查看可用话题
rostopic list

# 查看话题数据
rostopic echo /map
rostopic echo /odom

# 查看话题频率
rostopic hz /odom
```

### 3. 测试连接

```bash
# 测试WebSocket连接
wscat -c ws://localhost:9090

# 发送测试消息
{"op": "subscribe", "topic": "/map", "type": "nav_msgs/OccupancyGrid"}
```

## 性能优化

### 1. 减少数据传输

- 降低地图发布频率
- 使用地图压缩
- 限制激光雷达数据点数

### 2. 优化渲染性能

- 适当设置地图分辨率
- 限制最大缩放级别
- 使用硬件加速

## 扩展功能

### 1. 添加路径显示

```typescript
// 在initMapViewer中添加
const pathClient = new ROS2D.Path({
  ros: ros.value,
  topic: '/move_base/NavfnROS/plan',
  rootObject: viewer.scene,
  strokeColor: '#00ff00',
  strokeWidth: 2
});
```

### 2. 添加目标点设置

```typescript
// 添加鼠标点击事件
const handleMapClick = (event) => {
  // 将屏幕坐标转换为地图坐标
  const mapCoords = screenToMapCoords(event.clientX, event.clientY);
  
  // 发布目标点
  const goalTopic = new ROSLIB.Topic({
    ros: ros.value,
    name: '/move_base_simple/goal',
    messageType: 'geometry_msgs/PoseStamped'
  });
  
  goalTopic.publish({
    header: { frame_id: 'map' },
    pose: {
      position: { x: mapCoords.x, y: mapCoords.y, z: 0 },
      orientation: { x: 0, y: 0, z: 0, w: 1 }
    }
  });
};
```
