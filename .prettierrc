{"printWidth": 130, "tabWidth": 2, "useTabs": false, "semi": true, "singleQuote": false, "quoteProps": "as-needed", "jsxSingleQuote": false, "trailingComma": "none", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "requirePragma": false, "insertPragma": false, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": false, "endOfLine": "auto", "rangeStart": 0, "plugins": ["prettier-plugin-tailwindcss"], "tailwindStylesheet": "./src/style.css"}