{
  "compilerOptions": {
    "composite": true,
    "target": "ESNext",
    "lib": [
      "ESNext",
      "DOM",
      "Dom.Iterable",
      "esnext"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "verbatimModuleSyntax": true,
    "jsx": "preserve",
    "incremental": true,
    "types": [
      "vite/client",
      "naive-ui/volar.d.ts"
    ],
    "baseUrl": "./",
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "video.js": [
        "node_modules/@types/video.js"
      ]
    },
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "build/**/*.ts",
    "build/**/*.d.ts",
    "vite.config.ts"
  ],
  "exclude": [
    "node_modules",
    "dis",
    "**/*.js"
  ]
}