{"name": "intelligence-robot-dog", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint:prettier": "prettier --write \"src/**/*.{js,ts,json,tsx,css,scss,vue,html,md}\"", "lint": "eslint"}, "dependencies": {"@tailwindcss/vite": "^4.1.3", "@tanstack/vue-table": "^8.21.3", "@vee-validate/zod": "^4.15.0", "@vueuse/core": "^13.3.0", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "echarts": "^6.0.0", "flv.js": "^1.6.2", "lodash": "^4.17.21", "lucide-vue-next": "^0.487.0", "md5": "^2.3.0", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "qs": "^6.14.0", "reka-ui": "^2.3.1", "sass": "^1.89.2", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.3", "tw-animate-css": "^1.2.5", "vee-validate": "^4.15.0", "video.js": "^8.23.4", "videojs-flvjs-es6": "^1.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue-sonner": "^2.0.1", "zod": "^3.24.4"}, "devDependencies": {"@eslint/js": "^9.24.0", "@types/lodash": "^4.17.18", "@types/md5": "^2.3.5", "@types/mockjs": "^1.0.10", "@types/node": "^22.14.0", "@types/qs": "^6.14.0", "@types/video.js": "^7.3.58", "@typescript-eslint/parser": "^8.34.0", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/tsconfig": "^0.7.0", "eslint": "^9.24.0", "eslint-plugin-vue": "^10.0.0", "globals": "^16.0.0", "mockjs": "^1.1.0", "naive-ui": "^2.41.0", "picocolors": "^1.1.1", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "rollup-plugin-visualizer": "^5.14.0", "typescript": "~5.8.3", "typescript-eslint": "^8.29.1", "vite": "^6.2.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-mock": "^3.0.2", "vite-plugin-progress": "^0.0.7", "vue-tsc": "^2.2.4"}}