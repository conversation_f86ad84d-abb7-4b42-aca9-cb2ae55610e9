<template>
  <n-config-provider class="h-screen" :locale="zhCN" :date-locale="dateZhCN">
    <n-spin :show="requestStore.loading" class="h-screen [&>.n-spin-content]:h-screen">
      <n-message-provider>
        <n-dialog-provider>
          <n-modal-provider>
            <router-view></router-view>
          </n-modal-provider>
        </n-dialog-provider>
      </n-message-provider>
    </n-spin>
  </n-config-provider>
</template>

<script setup lang="ts">
import { NConfigProvider, NMessageProvider, NDialogProvider, NModalProvider } from "naive-ui";
import { zhCN, dateZhCN } from "naive-ui";
import { useRequestStore } from "@/store/modules/request";

const requestStore = useRequestStore();
</script>

<style scoped></style>
