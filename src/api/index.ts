import axios, { AxiosError } from "axios";
import type { AxiosInstance, AxiosRequestConfig, InternalAxiosRequestConfig, AxiosResponse } from "axios";
import { createDiscreteApi } from "naive-ui";

import { HttpResult } from "@/constants";
import type { ResultData } from "@/api/interface";

import { useRequestStore } from "@/store/modules/request";
import { useAuthStore } from "@/store/modules/auth";
import { useGlobalStore } from "@/store/modules/global";
import router from "@/router";

import { RoutePath } from "@/constants";

import { AxiosCanceler } from "./helper";

export interface CustomAxiosRequestConfig extends InternalAxiosRequestConfig {
  loading?: boolean;
  cancel?: boolean;
}

const { message } = createDiscreteApi(["message"], {
  messageProviderProps: {
    max: 1
  }
});

const config: AxiosRequestConfig = {
  baseURL: import.meta.env.VITE_API_URL,
  timeout: HttpResult.TIMEOUT,
  withCredentials: true
};

const axiosCanceler = new AxiosCanceler();

class Http {
  service: AxiosInstance;
  public constructor(config: AxiosRequestConfig) {
    this.service = axios.create(config);

    // Request interceptor
    this.service.interceptors.request.use(
      (config: CustomAxiosRequestConfig) => {
        const requestStore = useRequestStore();
        const authStore = useAuthStore();

        config.cancel ??= true;
        if (config.cancel) {
          axiosCanceler.addPending(config);
        }

        config.loading ??= true;
        if (config.loading) {
          requestStore.openLoading();
        }

        if (config.headers && typeof config.headers.set === "function") {
          config.headers.set("access-token", authStore.token);
        }
        return config;
      },
      (error: AxiosError) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.service.interceptors.response.use(
      (response: AxiosResponse & { config: CustomAxiosRequestConfig }) => {
        const { config, data } = response;

        const requestStore = useRequestStore();
        const authStore = useAuthStore();
        const globalStore = useGlobalStore();

        axiosCanceler.removePending(config);

        if (config.loading) {
          requestStore.closeLoading();
        }

        if (data.code === HttpResult.OVERDUE) {
          // authStore.setToken("");
          authStore.clear();
          globalStore.clear();
          router.replace(RoutePath.LOGIN);
          message.error(data.msg);
          return Promise.reject(data);
        }

        if (data.code && data.code !== HttpResult.SUCCESS) {
          message.error(data.msg);
          return Promise.reject(data);
        }

        return data;
      },
      (error: AxiosError) => {
        const { response } = error;
        const requestStore = useRequestStore();
        const authStore = useAuthStore();
        const globalStore = useGlobalStore();
        requestStore.closeLoading();
        if (error.message.indexOf("timeout") !== -1) message.error("请求超时！请您稍后重试");
        if (error.message.indexOf("Network Error") !== -1) message.error("网络异常！请您稍后重试");

        if (response) {
          switch (response.status) {
            case 400:
              message.error((response.data as any).message);
              break;
            case 401:
              message.error("登录失效！请您重新登陆");
              // authStore.setToken("");
              authStore.clear();
              globalStore.clear();
              router.replace(RoutePath.LOGIN);
              break;
            case 500:
              message.error("服务异常！");
              break;
          }
        }

        return Promise.reject(error);
      }
    );
  }

  get<T>(url: string, params?: object, _object = {}): Promise<ResultData<T>> {
    return this.service.get(url, { params, ..._object });
  }

  post<T>(url: string, params?: object | string, _object = {}): Promise<ResultData<T>> {
    return this.service.post(url, params, _object);
  }

  postForm<T>(url: string, params?: object | string, _object = {}): Promise<ResultData<T>> {
    return this.service.postForm(url, params, _object);
  }

  put<T>(url: string, params?: object, _object = {}): Promise<ResultData<T>> {
    return this.service.put(url, params, _object);
  }

  putForm<T>(url: string, params?: object, _object = {}): Promise<ResultData<T>> {
    return this.service.putForm(url, params, _object);
  }

  delete<T>(url: string, params?: any, _object = {}): Promise<ResultData<T>> {
    return this.service.delete(url, { params, ..._object });
  }
}

export default new Http(config);
