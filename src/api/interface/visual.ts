/** 机器人状态 */
export interface RobotStatusRes {
  /** 站点ID */
  substation: string;
  /** 机器人唯一标记 */
  robotSn: string;
  /** 机器人运行状态（参考枚举说明表：机器人实时状态-运行状态（7））*/
  runStatus: string;
  /** 云台通信状态（参考枚举说明表：机器人实时状态-在线状态（3））*/
  panStatus: string;
  /** 导航通信状态（参考枚举说明表：机器人实时状态-在线状态（3）） */
  naviStatus: string;
  /** 表计识别状态（参考枚举说明表：机器人实时状态-在线状态（3）） */
  regnStatus: string;
  /** 控制模式（参考枚举说明表：机器人实时状态-控制模式（5）） */
  mode: string;
  /** 机器人运行速度（m/s） */
  speed: number;
  /** 机器人运行时间（s） */
  runtime: number;
  /** 机器人行驶里程（m） */
  mileage: number;
  /** 所在站点地图的坐标X */
  posX: string;
  /** 所在站点地图的坐标Y */
  posY: string;
  /** 所在站点地图的坐标Z */
  posZ: string;
  /** 所在站点地图的朝向 */
  orientation: string;
  /** 当前所在位置经度 */
  posLng: string;
  /** 当前所在位置纬度 */
  posLat: string;
  /** 当前电量百分比 */
  batteryPercent: number;
  /** 充电电流 */
  chargeCurr: number;
  /** 充电电压 */
  chargeVoltage: number;
  /** 机身温度 */
  devTemp: number;
  /** 云台水平角度 */
  ptzHor: number;
  /** 云台垂直角度 */
  ptzVer: number;
  /** 电池温度 */
  batteryTemp: number;
  /** 是否遇到障碍物 */
  isObstacle: string;
  /** 温度（环境） */
  temperature: number;
  /** 湿度（环境） */
  humidity: number;
  /** 风速（环境） */
  windspeed: number;
  /** 雨刷状态（开/关） */
  wiper: string;
  /** 车灯状态（开/关） */
  light: string;
  /** 避障状态（开/关） */
  obstacle: string;
  /** 相机倍数 */
  cameraMultiple: number;
  /** 时间 */
  time: string;
}

export interface RobotNewWeatherRes {
  id: string;
  /** 站点ID */
  substation: string;
  /** 机器人唯一标记 */
  robotSn: string;
  /** 温度（°C） */
  temperature: number;
  /** 湿度（%） */
  humidity: number;
  /** 风速（m/s） */
  windSpeed: number;
  /** 风向（°或指向） */
  windDirection: string;
  /** 气压（hPa） */
  atmPressure: number;
  /** 雨量（mm） */
  rainAmount: number;
  /** 时间 */
  time: string;
}
