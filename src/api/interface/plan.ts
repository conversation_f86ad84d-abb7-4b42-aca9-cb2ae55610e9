import type { ReqPage } from "@/api/interface";

/**
 * 巡检任务列表 params
 */
export interface PlanListParams extends ReqPage {
  /** 名称 */
  taskName: string;
}

/**
 * 巡检任务列表 result
 */
export interface PlanListResult {
  /** 设备编号 */
  id: number;
  /** 机器人编号，关联机器人表主键 */
  robotId: number;
  robotName: string;
  /** 任务类型（0例行巡检1全面巡检2专项巡检3特殊巡检） */
  typeId: number;
  /** 任务名称 */
  planName: string;
  /** 是否有效 0否 1是 */
  state: string;
  /** 任务优先级别，值从0-9，值越大级别越高 */
  priority: number;
  /** 车速 */
  carSpeed: number;
  /** 绑定类型：0，绑定任务点，1，绑定观测点 */
  binType: number;
  /** 设备类型 */
  deviceType: string;
  /** 识别类型 */
  distinguishType: string;
  /** 表计类型 */
  meterType: string;
  /** 区域类型 */
  areaType: string;
  /** 表计类型 */
  insertTime: string;
  /** 编制人 */
  creator: number;
  /** 周期不可用JSON */
  quartzDisableJson: string;
  /** 周期可用JSON */
  quartzEnableJson: string;
  /** 机构编号 */
  orgId: number;
  /** 任务编号 */
  taskId: number;
  /** 是否删除,1是，0否 */
  isDeleted: number;
  /** 是否上传,1是，0否 */
  isUpload: number;
  /** 集控图片上传地址 */
  uploadUrl: string;
  patrolNodeIdList: number[];
  patrolNodeList: PatrolNodeListResult[];
}

export interface PlanSaveRequest {
  id?: number; //
  robotId?: number; //  机器人
  typeId?: number; // 任务类型
  planName?: string; //  任务名称
  state?: string; //  是否有效
  priority?: number; //  任务优先级
  carSpeed?: number; //  车速
  binType?: number; //
  deviceType?: string; //
  distinguishType?: string; //
  meterType?: string; //
  areaType?: string; //
  insertTime?: string; //
  creator?: number; //
  quartzDisableJson?: string; //
  quartzEnableJson?: string; //
  orgId?: number; //
  taskId?: number; //
  isDeleted?: number; //
  isUpload?: number; //
  uploadUrl?: string; //
  patrolNodeIdList?: number[];
}

/** 巡检点列表 */
export interface PatrolNodeListResult {
  carSpeed: number;
  createTime: string;
  gait: string;
  id: number;
  more: any;
  name: string;
  nodeType: string;
  orgId: string;
  orientation: string;
  robotId: number;
  x: string;
  y: string;
  z: string;
}

/**
 * 发送巡检任务执行指令
 */
export interface SendPatrolTaskMessageRequest {
  rbPlanId: number; // 巡检任务ID
  ctrlType: "2001" | "2002" | "2003" | "2004" | "2005" | "2006"; // 控制类型（终止2001，启动2002，暂停2003，恢复2004，超期2005，完成2006，最后两个仅代表任务状态）
}
