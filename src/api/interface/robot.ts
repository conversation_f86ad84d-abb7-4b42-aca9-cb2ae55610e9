import type { ReqPage } from ".";

export interface RobotListResult {
  id: number; // 机器人设备的唯一编号，主键ID
  robotName: string; // 机器人的自定义名称或别名
  robotMac: string; // 机器人的物理编码或序列号，通常为硬件唯一标识
  rosSoftVersion: string; // 机器人操作系统(ROS)的软件版本号
  rosMatchVersion: string; // 机器人操作系统(ROS)所依赖的硬件版本号
  yuntaiSoftVersion: string; // 搭载的云台设备的软件固件版本
  yuntaiMatchVersion: string; // 搭载的云台设备的硬件版本号
  yuntaiSocketIp: string; // 云台设备的网络IP地址
  yuntaiSocketPort: string; // 用于向云台发送控制命令的服务器端口号
  yuntaiFtpIp: string; // 云台内置文件服务器的IP地址，用于文件传输
  yuntaiFtpPort: string; // 云台内置文件服务器的端口号
  yuntaiFtpUser: string; // 访问云台文件服务器的用户名
  yuntaiFtpPwd: string; // 访问云台文件服务器的密码
  yuntaiIrUrl: string; // 获取红外视频流的URL地址
  yuntaiTvUrl: string; // 获取可见光视频流的URL地址
  state: number; // 机器人的在线或工作状态，具体枚举值需参照业务定义
  manufacturerId: string; // 机器人生产厂家的ID或代码
  deviceType: string; // 机器人的具体型号
  manufactureTime: string; // 机器人的出厂日期，格式通常为YYYY-MM-DD
  manufactureNum: string; // 工厂赋予的唯一出厂序列号
  commissioningDate: string; // 机器人正式投入使用的日期
  orgId: string; // 机器人所属的组织或机构ID
  isTransport: number; // 机器人是否参与轮转任务，0代表不轮转，1代表轮转
  batteryCapacity: string; // 机器人电池的额定容量，单位通常为mAh或Wh
  navigationTypeId: string; // 机器人采用的导航技术类型ID，如激光SLAM、视觉导航等
  typeId: string; // 机器人的大类分类ID，如巡检机器人、配送机器人等
  driveTypeId: string; // 机器人的驱动方式ID，如轮式、履带式等
  totalMileage: number; // 机器人自投运以来累计运行的总里程，单位为公里(km)
  yuntaiAppIrUrl: string; // 专供APP访问的红外视频流地址
  yuntaiAppTvUrl: string; // 专供APP访问的可见光视频流地址
  horizonDegree: number; // 摄像头水平方向的最大可视角，单位为度(°)
  verticalDegree: number; // 摄像头垂直方向的最大可视角，单位为度(°)
  runModel: string; // 机器人当前的运行模式，如充电、巡检、待机等
  channelCode: string; // 用于视频平台或流媒体服务的通道标识编码
}

/**
 * 字典数据
 */
export interface DictDataResult {
  id: number; //
  dictTypeId: number; // 字典类型ID
  dictLabel: string; // 字典标签
  dictValue: number; // 字典value
  remark: string;
}

/**
 * 机器人告警信息 Request
 */
export interface RobotAlarmRequest extends ReqPage {
  robotId?: number; // 机器人ID
  reviewState?: number; // 告警状态
  level?: string; // 告警级别
  startTime?: string; // 起始时间 yyyy-MM-dd HH:mm:ss
  endTime?: string; // 结束时间
}

/**
 * 机器人告警信息 Response
 */
export interface RobotAlarmResponse {
  id: number; // 告警记录的唯一编号
  module: string; // 发生告警的机器人部件名称
  cause: string; // 告警发生的原因
  createTime: string; // 告警记录的创建时间
  robotId: number; // 发生告警的机器人ID
  paramter: string; // 告警发生时的相关参数信息
  alarmType: string; // 机器人本体的告警类型分类
  alarmInfo: string; // 对告警的规范性描述信息
  reviewState: number; // 告警的审核状态，1代表已确认，0代表未确认
  reviewTime: string; // 告警被审核确认的时间
  level: string; // 告警的严重级别
  fun: string; // 告警发生时机器人正在执行的功能
  userId: number; // 审核该告警的用户ID
  userName: string;
  resultCode: string; // 告警处理的结果码
  isUpload: string; // 该告警记录是否已上传，0代表否，1代表是
}

/**
 * 机器人告警信息修改 Request
 */
export interface AlarmReviewModuleRequest {
  id: number;
  reviewInfo: string;
}

/** 摄像头列表 */
export interface CameraDeviceResult {
  id: number; // 唯一ID
  cameraId: number; // 设备ID
  groupId: number; // 所属设备分组ID
  cameraType: number; // 设备类型1摄像头2NVR
  cameraName: string; // 设备名称
  cameraLocation: string; // 安装位置
  cameraMode: number; // 设备模式1视频流2抓图服务
  accessMode: number; // 接入方式1流媒体
  flvUrl: string; // flv播放地址
  rtspUrl: string; // RTSP流播放地址
  algorithmVideoUrl: string; // 带算法框的视频播放地址
  remark: string; // 备注
  isOnline: number; // 是否在线0不在线1在线
  status: number; // 0停用1启用
  isDeleted: number; // 是否删除0否1是
  creator: number; // 创建者
  createDate: string; // 创建时间
  updater: number; // 更新者
  updateDate: string; // 更新时间
  algorithmCount: number; // 算法数
}
