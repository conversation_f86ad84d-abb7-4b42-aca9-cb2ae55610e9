import http from "@/api";

import type { ResultData } from "@/api/interface";
import type { RobotStatusRes, RobotNewWeatherRes } from "@/api/interface/visual";

export const robotStatusApi = () => {
  return http.get<RobotStatusRes>("/rb_running/getRobotNewStatus", {}, { loading: false });
};

/**
 * @description 获取机器人最新气象数据
 * @returns {Promise<RobotNewWeatherRes>}
 */
export const robotNewWeatherApi = (): Promise<ResultData<RobotNewWeatherRes>> => {
  return http.get<RobotNewWeatherRes>("/rb_weather/getRobotNewWeather", {}, { loading: false });
};
