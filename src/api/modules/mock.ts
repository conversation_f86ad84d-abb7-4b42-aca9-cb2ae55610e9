import http from "@/api";

import type { ReqPage } from "@/api/interface";

const config = { baseURL: "/mock" };

export const getIntelligenceTaskListApi = (params: ReqPage) => {
  return http.get("/intelligence/task", params, config);
};

export const getIntelligenceTreeApi = () => {
  return http.get("/intelligence/tree", {}, config);
};

export const getDailyTaskListApi = (params: ReqPage) => {
  return http.get("/daily/task", params, config);
};

export const overallStatusApi = () => {
  return http.get("/visual/overall-status", {}, { loading: false, ...config });
};

export const overallLedgerListAPi = (params: ReqPage) => {
  return http.get("/visual/ledger-list", params, { loading: false, ...config });
};

export const overallRobotStatusAPi = (params: ReqPage) => {
  return http.get("/visual/robot-status", params, { loading: false, ...config });
};

export const overallTaskInfoListApi = (params: ReqPage) => {
  return http.get("/visual/task-info-list", params, { loading: false, ...config });
};

export const overallTaskInfoApi = (params: any, _config: any) => {
  return http.get<any[]>("/visual/task-info", params, { ..._config, ...config });
};

export const overallConsoleLogApi = () => {
  return http.get<any[]>("/visual/console-log", {}, { loading: false, ...config });
};

export const overallInspectionInfoApi = () => {
  return http.get("/visual/inspection-info", {}, { loading: false, ...config });
};
