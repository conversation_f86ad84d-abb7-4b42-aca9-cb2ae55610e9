import http from "@/api";
import type { PlanListParams, PlanSaveRequest, PatrolNodeListResult, SendPatrolTaskMessageRequest } from "@/api/interface/plan";

/**
 * 巡检计划列表
 * @param params
 * @returns
 */
export const planListApi = (params: PlanListParams) => {
  return http.get("/rbPlan/planList", params);
};

export const planSaveApi = (params: PlanSaveRequest) => {
  return http.post("/rbPlan", params);
};

export const planEditApi = (params: PlanSaveRequest) => {
  return http.put("/rbPlan", params);
};

export const planDeleteApi = (params: { id: number }) => {
  return http.delete("/rbPlan", params);
};

/**
 * 巡检点列表
 * @returns
 */
export const patrolNodeListApi = (params: any, config: any) => {
  return http.get<PatrolNodeListResult[]>("/rbPatrolNode/patrolNodeList", params, config);
};

/**
 * 发送巡检任务执行指令
 * @param params
 * @returns
 */
export const sendPatrolTaskMessageApi = (params: SendPatrolTaskMessageRequest) => {
  return http.postForm("/rbPlan/sendPatrolTaskMessage", params);
};
