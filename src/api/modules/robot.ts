import http from "@/api";

import type { ResPage } from "@/api/interface";
import type {
  RobotListResult,
  DictDataResult,
  RobotAlarmRequest,
  RobotAlarmResponse,
  AlarmReviewModuleRequest,
  CameraDeviceResult
} from "@/api/interface/robot";

/**
 * 机器狗列表
 * @returns
 */
export const robotListApi = () => {
  return http.get<RobotListResult[]>("/rbRobot/robotList");
};

/**
 * 字典数据
 */
export const dictDataApi = () => {
  return http.get<DictDataResult[]>("/rbRobot/dictData");
};

/**
 * 机器人告警信息
 * @param params
 * @returns
 */
export const robotAlarmListApi = (params: RobotAlarmRequest) => {
  return http.get<ResPage<RobotAlarmResponse>>("/rbRobotModuleState/robotModulePage", params);
};

/**
 * 机器人告警信息审核
 * @param params
 * @returns
 */
export const alarmReviewModuleApi = (params: AlarmReviewModuleRequest) => {
  return http.postForm("/rbRobotModuleState/reviewModule", params);
};

export const cameraDeviceListApi = () => {
  return http.get<ResPage<CameraDeviceResult>>("/device/camera/page", { page: 1, limit: 10 });
};
