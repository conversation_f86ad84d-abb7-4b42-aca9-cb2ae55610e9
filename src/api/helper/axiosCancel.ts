import type { CustomAxiosRequestConfig } from "../index";
import qs from "qs";

const pendingMap = new Map<string, AbortController>();

const sortedStringify = (obj: any) => {
  return qs.stringify(obj, { arrayFormat: "repeat", sort: (a, b) => a.localeCompare(b) });
};

export const getPendingUrl = (config: CustomAxiosRequestConfig) => {
  return [config.method, config.url, sortedStringify(config.data), sortedStringify(config.params)].join("&");
};

export class AxiosCanceler {
  addPending(config: CustomAxiosRequestConfig) {
    this.removePending(config);
    const url = getPendingUrl(config);
    const controller = new AbortController();
    config.signal = controller.signal;
    pendingMap.set(url, controller);
  }

  removePending(config: CustomAxiosRequestConfig) {
    const url = getPendingUrl(config);

    const controller = pendingMap.get(url);

    if (controller) {
      controller.abort();
      pendingMap.delete(url);
    }
  }

  removeAllPending() {
    pendingMap.forEach(controller => {
      controller?.abort();
    });
    pendingMap.clear();
  }
}
