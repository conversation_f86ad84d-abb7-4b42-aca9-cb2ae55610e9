import { ref, onMounted } from 'vue'

interface LabelItem {
  label: string
  [key: string]: any
}

interface UseLabelWidthOptions {
  fontSize?: string
  suffix?: string
  extraWidth?: number
}

/**
 * 计算每个列表label最大宽度的钩子
 * @param labelLists 包含label的数组列表
 * @param options 配置选项
 * @returns 每个列表的最大宽度数组
 */
export function useLabelWidth(
  labelLists: LabelItem[][], 
  options: UseLabelWidthOptions = {}
) {
  const { fontSize = '14px', suffix = '：', extraWidth = 0 } = options
  const maxLabelWidths = ref<number[]>([])

  // 计算每个列表的最大label宽度
  const calculateMaxLabelWidths = () => {
    const tempDiv = document.createElement('div')
    tempDiv.style.position = 'absolute'
    tempDiv.style.visibility = 'hidden'
    tempDiv.style.whiteSpace = 'nowrap'
    tempDiv.style.fontSize = fontSize
    document.body.appendChild(tempDiv)
    
    const widths: number[] = []
    
    // 为每个列表计算最大宽度
    labelLists.forEach((list, index) => {
      let maxWidth = 0
      list.forEach(item => {
        tempDiv.textContent = item.label + suffix
        const width = tempDiv.offsetWidth
        if (width > maxWidth) {
          maxWidth = width
        }
      })
      widths[index] = maxWidth + extraWidth
    })
    
    document.body.removeChild(tempDiv)
    maxLabelWidths.value = widths
  }

  // 获取指定列表的label样式
  const getLabelStyle = (listIndex: number, width?: number) => {
    return {
      width: `${width || maxLabelWidths.value[listIndex] || 0}px`
    }
  }

  onMounted(() => {
    calculateMaxLabelWidths()
  })

  return {
    maxLabelWidths,
    calculateMaxLabelWidths,
    getLabelStyle
  }
} 