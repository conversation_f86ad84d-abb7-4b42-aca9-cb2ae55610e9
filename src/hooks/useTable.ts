import { reactive, computed, toRefs } from "vue";
import type { StateProps } from "./interface";

export const useTable = (
  api?: (params: any) => Promise<any>,
  initParam: object = {},
  isPageable: boolean = true,
  dataCallBack?: (_data: any) => any,
  requestError?: (_error: any) => void,
  paramProcessor?: (_params: Record<string, any>) => Record<string, any>
) => {
  const state = reactive<StateProps>({
    tableData: [],
    pageable: {
      page: 1,
      limit: 10,
      total: 0
    },
    // 查询参数(只包括查询)
    searchParam: {},
    // 初始化默认的查询参数
    searchInitParam: {},
    // 总参数(包含分页和查询参数)
    totalParam: {},
    loading: false
  });

  const pageParam = computed({
    get: () => {
      return {
        page: state.pageable.page,
        limit: state.pageable.limit
      };
    },
    set: () => {}
  });

  const getTableList = async () => {
    if (!api) return;

    try {
      state.loading = true;
      Object.assign(state.totalParam, initParam, isPageable ? pageParam.value : {});
      let { data } = await api({ ...state.searchInitParam, ...state.totalParam });
      if (dataCallBack) {
        data = dataCallBack(data);
      }
      state.tableData = isPageable ? data.records : data;
      if (isPageable) {
        state.pageable.total = data.total;
      }
    } catch (error) {
      if (requestError) {
        requestError(error);
      }
    } finally {
      state.loading = false;
    }
  };

  /**
   * @description 更新查询参数
   * @return void
   * */
  const updatedTotalParam = () => {
    state.totalParam = {};

    // 处理查询参数，可以给查询参数加自定义前缀操作
    const nowSearchParam: StateProps["searchParam"] = {};
    // 防止手动清空输入框携带参数（这里可以自定义查询参数前缀）
    for (const key in state.searchParam) {
      // 某些情况下参数为 false/0 也应该携带参数
      if (state.searchParam[key] || state.searchParam[key] === false || state.searchParam[key] === 0) {
        nowSearchParam[key] = state.searchParam[key];
      }
    }

    const processedParams = paramProcessor ? paramProcessor(nowSearchParam) : nowSearchParam;
    Object.assign(state.totalParam, processedParams);
  };

  /**
   * @description 表格数据查询
   * @return void
   * */
  const search = () => {
    state.pageable.page = 1;
    updatedTotalParam();
    getTableList();
  };

  /**
   * @description 表格数据重置
   * @return void
   * */
  const reset = () => {
    state.pageable.page = 1;
    // 重置搜索表单的时，如果有默认搜索参数，则重置默认的搜索参数
    // state.searchParam = { ...state.searchInitParam };
    for (const key in state.searchParam) {
      state.searchParam[key] = null;
    }
    updatedTotalParam();
    getTableList();
  };

  /**
   * @description 每页条数改变
   * @param {Number} val 当前条数
   * @return void
   * */
  const handleSizeChange = (val: number) => {
    state.pageable.page = 1;
    state.pageable.limit = val;
    getTableList();
  };

  /**
   * @description 当前页改变
   * @param {Number} val 当前页
   * @return void
   * */
  const handleCurrentChange = (val: number) => {
    state.pageable.page = val;
    getTableList();
  };

  return {
    ...toRefs(state),
    getTableList,
    search,
    reset,
    handleSizeChange,
    handleCurrentChange,
    updatedTotalParam
  };
};
