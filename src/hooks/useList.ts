import { ref, type Ref } from "vue";

interface ApiResponse<T> {
  data: T[];
  [key: string]: any;
}

type ApiFunction<T> = (params?: object, config?: object) => Promise<ApiResponse<T>>;

type UseListReturn<T> = [Ref<T[]>, (params?: object, config?: object) => Promise<void>, Ref<boolean>];

export const useList = <T = any>(api: ApiFunction<T>): UseListReturn<T> => {
  const list = ref<T[]>([]) as Ref<T[]>;
  const loading = ref(false);

  const fetch = async (params?: object, config?: object): Promise<void> => {
    loading.value = true;

    try {
      const response = await api(params, config);
      list.value = response.data || [];
    } catch {
      list.value = [];
    } finally {
      loading.value = false;
    }
  };

  return [list, fetch, loading];
};
