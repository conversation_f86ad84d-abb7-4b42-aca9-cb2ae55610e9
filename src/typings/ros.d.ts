declare global {
  // ROSLIB 类型定义
  namespace ROSLIB {
    class Ros {
      constructor(options: { url: string });

      on(event: "connection" | "error" | "close", callback: (data?: any) => void): void;
      off(event: string, callback: (data?: any) => void): void;

      // 连接状态
      isConnected: boolean;

      // 关闭连接
      close(): void;
    }

    class Topic {
      constructor(options: { ros: Ros; name: string; messageType: string });

      subscribe(callback: (message: any) => void): void;
      unsubscribe(): void;
      publish(message: any): void;
    }

    class Service {
      constructor(options: { ros: Ros; name: string; serviceType: string });

      callService(request: any, callback: (result: any) => void): void;
    }

    class Param {
      constructor(options: { ros: Ros; name: string });

      get(callback: (value: any) => void): void;
      set(value: any, callback?: (result: any) => void): void;
      delete(callback?: (result: any) => void): void;
    }
  }

  // ROS2D 类型定义
  namespace ROS2D {
    class Viewer {
      constructor(options: { divID: string; width: number; height: number });

      // 场景对象
      scene: createjs.Container;

      // 缩放方法
      scaleToDimensions(width: number, height: number): void;

      // 移动方法
      shift(x: number, y: number): void;

      // 缩放属性
      scaleX: number;
      scaleY: number;

      // 添加和移除对象
      addChild(child: createjs.DisplayObject): void;
      removeChild(child: createjs.DisplayObject): void;
    }

    class OccupancyGridClient {
      constructor(options: { ros: ROSLIB.Ros; rootObject: createjs.Container; topic: string });

      // 根对象
      rootObject: createjs.Container;

      // 当前网格数据
      currentGrid: {
        width: number;
        height: number;
        pose: {
          position: {
            x: number;
            y: number;
          };
        };
      };

      // 事件监听
      on(event: "change", callback: () => void): void;
      off(event: string, callback: () => void): void;
    }

    class PoseWithCovariance {
      constructor(options: { ros: ROSLIB.Ros; topic: string; rootObject: createjs.Container });

      on(event: "change", callback: (pose: any) => void): void;
    }

    class LaserScan {
      constructor(options: { ros: ROSLIB.Ros; topic: string; rootObject: createjs.Container });

      on(event: "change", callback: (scan: any) => void): void;
    }

    class Path {
      constructor(options: {
        ros: ROSLIB.Ros;
        topic: string;
        rootObject: createjs.Container;
        strokeColor?: string;
        strokeWidth?: number;
      });

      on(event: "change", callback: (path: any) => void): void;
    }

    class Marker {
      constructor(options: { ros: ROSLIB.Ros; topic: string; rootObject: createjs.Container });

      on(event: "change", callback: (marker: any) => void): void;
    }

    class NavigationArrow {
      constructor(options: {
        size?: number;
        strokeSize?: number;
        fillColor?: string;
        pulse?: boolean;
      });

      x: number;
      y: number;
      rotation: number;
      visible: boolean;
    }

    class ZoomView {
      constructor(options: { rootObject: any; minScale?: number });
      startZoom(x: number, y: number): void;
      zoom(factor: number): void;
    }

    class PanView {
      constructor(options: { rootObject: any });
      startPan(x: number, y: number): void;
      pan(x: number, y: number): void;
    }
  }

  // CreateJS 类型定义（ROS2D 依赖）
  namespace createjs {
    class DisplayObject {
      x: number;
      y: number;
      scaleX: number;
      scaleY: number;
      rotation: number;
      alpha: number;
      visible: boolean;

      addChild(child: DisplayObject): void;
      removeChild(child: DisplayObject): void;
    }

    class Container extends DisplayObject {
      children: DisplayObject[];
    }

    class Shape extends DisplayObject {
      graphics: Graphics;
    }

    class Graphics {
      beginFill(color: string): Graphics;
      beginStroke(color: string): Graphics;
      setStrokeStyle(width: number): Graphics;
      drawCircle(x: number, y: number, radius: number): Graphics;
      drawRect(x: number, y: number, width: number, height: number): Graphics;
      moveTo(x: number, y: number): Graphics;
      lineTo(x: number, y: number): Graphics;
      endFill(): Graphics;
      endStroke(): Graphics;
    }

    class Text extends DisplayObject {
      constructor(text: string, font: string, color: string);
      text: string;
      font: string;
      color: string;
      textAlign: string;
      textBaseline: string;
    }
  }
}
export {};
