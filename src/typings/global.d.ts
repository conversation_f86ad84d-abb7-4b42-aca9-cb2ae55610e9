declare interface ViteEnv {
  VITE_PORT: number;
  VITE_OPEN: boolean;
  VITE_GLOBAL_APP_TITLE: string;
  VITE_USER_NODE_ENV: "development" | "production";
  VITE_BUILD_COMPRESS: "gzip" | "brotli" | "gzip,brotli" | "none";
  VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE: boolean;
  VITE_DROP_CONSOLE: boolean;
  VITE_PUBLIC_PATH: string;
  VITE_API_URL: string;
  VITE_PROXY: [string, string][];
  VITE_ROUTER_MODE: string;
  /** 可见光 URL ID */
  VITE_VISIBLE_LIGHT: string;
  /** 红外 URL ID */
  VITE_INFRARED: string;
}

interface ImportMetaEnv extends ViteEnv {
  __: unknown;
}

/* Vite */
declare type Recordable<T = any> = Record<string, T>;

declare namespace Menu {
  interface MenuOptions {
    path: string;
    name: string;
    component?: string | (() => Promise<unknown>);
    redirect?: string;
    meta: MetaProps;
    children?: MenuOptions[];
  }

  interface MetaProps {
    icon: string;
    title: string;
    // activeMenu?: string;
    // isLink?: string;
    isHide: boolean;
    isFull: boolean;
    // isAffix: boolean;
    // isKeepAlive: boolean;
  }
}

declare module "vite-plugin-eslint" {
  import { PluginOption } from "vite";
  const eslint: () => PluginOption;
  export default eslint;
}

declare type WsDataTopicType = "xxx/robot/event/robotRunningStatus";

declare type WsDataType = {
  data: string;
  timestamp: number;
  topic: WsDataTopicType;
  type: string;
};
