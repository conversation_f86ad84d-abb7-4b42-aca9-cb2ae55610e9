/**
 * ROS配置文件
 * 用于配置ROS连接参数和话题名称
 */

export interface RosConfig {
  /** WebSocket连接URL */
  url: string;
  /** 重连配置 */
  reconnect: {
    /** 是否自动重连 */
    enabled: boolean;
    /** 重连间隔(毫秒) */
    interval: number;
    /** 最大重连次数 */
    maxAttempts: number;
  };
  /** ROS话题配置 */
  topics: {
    /** 地图话题 */
    map: string;
    /** 机器人位置话题 */
    robotPose: string;
    /** 激光雷达话题 */
    laserScan: string;
    /** 路径规划话题 */
    path: string;
    /** 目标点话题 */
    goalPose: string;
    /** 机器人状态话题 */
    robotStatus: string;
  };
  /** 地图显示配置 */
  map: {
    /** 默认宽度 */
    defaultWidth: number;
    /** 默认高度 */
    defaultHeight: number;
    /** 最小缩放比例 */
    minScale: number;
    /** 最大缩放比例 */
    maxScale: number;
    /** 缩放步长 */
    scaleStep: number;
  };
}

/**
 * 默认ROS配置
 * 请根据您的实际ROS环境修改这些参数
 */
export const defaultRosConfig: RosConfig = {
  // ROS Bridge WebSocket地址
  // 格式: ws://[ROS_BRIDGE_IP]:[PORT]/websocket/[ROBOT_ID]
  url: "ws://************:18086/websocket/567",
  
  reconnect: {
    enabled: true,
    interval: 3000, // 3秒
    maxAttempts: 5
  },
  
  topics: {
    // 地图数据话题 - 通常由map_server或SLAM算法发布
    map: "/map",
    
    // 机器人位置话题 - 里程计数据
    robotPose: "/odom",
    
    // 激光雷达数据话题
    laserScan: "/scan",
    
    // 路径规划话题 - 通常由move_base发布
    path: "/move_base/NavfnROS/plan",
    
    // 目标点话题 - 用于发送导航目标
    goalPose: "/move_base_simple/goal",
    
    // 机器人状态话题
    robotStatus: "/robot_status"
  },
  
  map: {
    defaultWidth: 800,
    defaultHeight: 600,
    minScale: 0.1,
    maxScale: 5.0,
    scaleStep: 0.1
  }
};

/**
 * 开发环境配置
 */
export const devRosConfig: RosConfig = {
  ...defaultRosConfig,
  url: "ws://localhost:9090", // 本地开发环境
};

/**
 * 生产环境配置
 */
export const prodRosConfig: RosConfig = {
  ...defaultRosConfig,
  url: "ws://************:18086/websocket/567", // 生产环境
};

/**
 * 获取当前环境的ROS配置
 */
export const getRosConfig = (): RosConfig => {
  const env = import.meta.env.MODE;
  
  switch (env) {
    case 'development':
      return devRosConfig;
    case 'production':
      return prodRosConfig;
    default:
      return defaultRosConfig;
  }
};

/**
 * ROS连接状态枚举
 */
export enum RosConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error',
  RECONNECTING = 'reconnecting'
}

/**
 * ROS消息类型定义
 */
export interface RosMessage {
  /** 消息时间戳 */
  timestamp: number;
  /** 消息类型 */
  type: string;
  /** 消息数据 */
  data: any;
}

/**
 * 地图元数据接口
 */
export interface MapMetadata {
  /** 地图分辨率(米/像素) */
  resolution: number;
  /** 地图宽度(像素) */
  width: number;
  /** 地图高度(像素) */
  height: number;
  /** 地图原点位置 */
  origin: {
    position: {
      x: number;
      y: number;
      z: number;
    };
    orientation: {
      x: number;
      y: number;
      z: number;
      w: number;
    };
  };
}

/**
 * 机器人位置接口
 */
export interface RobotPose {
  /** 位置 */
  position: {
    x: number;
    y: number;
    z: number;
  };
  /** 方向 */
  orientation: {
    x: number;
    y: number;
    z: number;
    w: number;
  };
  /** 协方差矩阵 */
  covariance?: number[];
}
