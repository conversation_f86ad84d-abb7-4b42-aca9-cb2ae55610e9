<template>
  <div class="flex h-full flex-col">
    <!-- 表格容器 -->
    <TooltipProvider>
      <div class="flex-1 overflow-hidden rounded-lg border border-slate-600/30 bg-slate-700/20">
        <!-- 加载状态 -->
        <div v-if="loading" class="flex h-full items-center justify-center">
          <div class="flex items-center space-x-2">
            <div class="h-4 w-4 animate-spin rounded-full border-2 border-slate-600 border-t-blue-400"></div>
            <span class="text-sm text-slate-400">{{ loadingText }}</span>
          </div>
        </div>

        <!-- 空数据状态 -->
        <div v-else-if="data.length === 0" class="flex h-32 items-center justify-center">
          <span class="text-sm text-slate-500">{{ emptyText }}</span>
        </div>

        <!-- 表格 -->
        <ScrollArea v-else class="h-full">
          <table class="w-full table-fixed border-collapse">
            <!-- 表头 -->
            <thead class="sticky top-0 z-10 border-b border-slate-600/30 bg-slate-600">
              <tr>
                <th
                  v-for="column in columns"
                  :key="column.key"
                  :style="getColumnStyle(column)"
                  class="border-r border-slate-600/20 p-3 text-center text-xs font-medium text-slate-300 last:border-r-0"
                >
                  {{ column.title }}
                </th>
              </tr>
            </thead>

            <!-- 表体 -->
            <tbody>
              <tr
                v-for="(item, index) in data"
                :key="getRowKey ? getRowKey(item, index) : index"
                class="border-b border-slate-600/20 bg-slate-700/10 transition-colors duration-150 hover:bg-slate-600/20"
                @click="handleRowClick(item, index)"
              >
                <td
                  v-for="column in columns"
                  :key="column.key"
                  :style="getColumnStyle(column)"
                  :class="[
                    'border-r border-slate-600/20 p-3 text-center last:border-r-0',
                    column.noWrap ? 'whitespace-nowrap' : ''
                  ]"
                >
                  <!-- 自定义渲染 (h函数) -->
                  <template v-if="column.render">
                    <template v-if="Array.isArray(column.render({ rowData: item, index }))">
                      <component
                        v-for="(vnode, vnodeIndex) in column.render({ rowData: item, index })"
                        :key="vnodeIndex"
                        :is="vnode"
                      />
                    </template>
                    <component v-else :is="column.render({ rowData: item, index })" />
                  </template>
                  <!-- 插槽渲染 -->
                  <slot v-else-if="column.slot" :name="column.slot" :value="item[column.key]" :row="item" :index="index">
                    <span :class="column.cellClass">
                      {{ formatCellValue(item[column.key]) }}
                    </span>
                  </slot>
                  <!-- 默认渲染 -->
                  <div v-else class="w-full">
                    <span v-if="!column.ellipsis" :class="column.cellClass">
                      {{ formatCellValue(item[column.key]) }}
                    </span>

                    <!-- 文字溢出处理 -->
                    <Tooltip v-else-if="getEllipsisTooltip(column.ellipsis)">
                      <TooltipTrigger as-child>
                        <div :class="['w-full cursor-default overflow-hidden text-ellipsis whitespace-nowrap', column.cellClass]">
                          {{ formatCellValue(item[column.key]) }}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent class="max-w-xs border-slate-600 text-slate-200">
                        <p class="text-sm">{{ formatCellValue(item[column.key]) }}</p>
                      </TooltipContent>
                    </Tooltip>

                    <!-- 只省略不显示 tooltip -->
                    <div v-else :class="['w-full overflow-hidden text-ellipsis whitespace-nowrap', column.cellClass]">
                      {{ formatCellValue(item[column.key]) }}
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </ScrollArea>
      </div>
    </TooltipProvider>

    <!-- 分页器 -->
    <div v-if="showPagination" class="mt-4 flex items-center justify-between flex-wrap gap-4">
      <!-- 动态渲染分页模块 -->
      <template v-for="module in displayOrder" :key="module">
        <!-- 信息模块 -->
        <div v-if="module === 'info'" class="text-xs text-slate-400">
          共 {{ pagination.total }} 条记录，第 {{ pagination.page }} / {{ totalPages }} 页
        </div>

        <!-- 每页条数选择器 -->
        <div v-else-if="module === 'size-picker' && showPageSizeSelector" class="flex items-center space-x-2">
          <span class="text-xs text-slate-400">每页</span>
          <Select :model-value="String(pagination.limit)" @update:model-value="handlePageSizeChange">
            <SelectTrigger class="h-8 w-20 border-slate-500/50 bg-slate-600/50 text-xs text-slate-300">
              <SelectValue />
            </SelectTrigger>
            <SelectContent class="border-slate-600 bg-slate-700">
              <SelectItem
                v-for="size in pageSizeOptions"
                :key="size"
                :value="String(size)"
                class="text-slate-300 hover:bg-slate-600 focus:bg-slate-600"
              >
                {{ size }}
              </SelectItem>
            </SelectContent>
          </Select>
          <span class="text-xs text-slate-400">条</span>
        </div>

        <!-- 分页按钮 -->
        <Pagination
          v-else-if="module === 'pages'"
          :total="pagination.total"
          :items-per-page="pagination.limit"
          :page="pagination.page"
          :sibling-count="1"
          :show-edges="true"
          @update:page="handlePageChange"
          class="w-auto justify-start"
        >
          <PaginationContent v-slot="{ items }" class="flex items-center gap-1">
            <PaginationPrevious
              class="h-8 w-8 border border-slate-500/50 bg-slate-600/50 p-0 text-slate-300 transition-all duration-200 hover:border-slate-400/50 hover:bg-slate-500/50 hover:text-slate-100 disabled:cursor-not-allowed disabled:opacity-50"
            >
              <ChevronLeftIcon />
            </PaginationPrevious>

            <template v-for="(item, index) in items" :key="index">
              <PaginationItem
                v-if="item.type === 'page'"
                :value="item.value"
                :is-active="item.value === pagination.page"
                class="h-8 w-8 border p-0 text-xs font-medium transition-all duration-200"
                :class="[
                  item.value === pagination.page
                    ? 'border-blue-500 bg-blue-500 text-white shadow-sm hover:border-blue-600 hover:bg-blue-600'
                    : 'border-slate-500/50 bg-slate-600/50 text-slate-300 hover:border-slate-400/50 hover:bg-slate-500/50 hover:text-slate-100'
                ]"
              >
                {{ item.value }}
              </PaginationItem>

              <PaginationEllipsis
                v-else-if="item.type === 'ellipsis'"
                :index="index"
                class="flex h-8 w-8 items-center justify-center text-slate-400"
              >
                <span class="text-sm">...</span>
              </PaginationEllipsis>
            </template>

            <PaginationNext
              class="h-8 w-8 border border-slate-500/50 bg-slate-600/50 p-0 text-slate-300 transition-all duration-200 hover:border-slate-400/50 hover:bg-slate-500/50 hover:text-slate-100 disabled:cursor-not-allowed disabled:opacity-50"
            >
              <ChevronRightIcon />
            </PaginationNext>
          </PaginationContent>
        </Pagination>

        <!-- 快速跳转 -->
        <div v-else-if="module === 'quick-jumper' && showQuickJumper" class="flex items-center space-x-2">
          <span class="text-xs text-slate-400">跳至</span>
          <input
            v-model="quickJumpValue"
            @keyup.enter="handleQuickJump"
            @blur="handleQuickJump"
            type="number"
            :min="1"
            :max="totalPages"
            class="h-8 w-16 rounded border border-slate-500/50 bg-slate-600/50 px-2 text-xs text-slate-300 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
          <span class="text-xs text-slate-400">页</span>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, type VNode } from "vue";
import { ChevronLeftIcon, ChevronRightIcon } from "lucide-vue-next";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationNext,
  PaginationPrevious
} from "@/components/ui/pagination";

// ==================== TypeScript 接口 ====================
interface TableColumn<T = any> {
  key: keyof T | string;
  title: string;
  width?: string;
  cellClass?: string;
  ellipsis?: boolean | { tooltip?: boolean }; // 文字溢出省略配置
  noWrap?: boolean; // 是否禁止换行
  render?: (params: { rowData: T; index: number }) => VNode | VNode[];
  slot?: string; // 插槽名称
}

interface PaginationData {
  page: number;
  limit: number;
  total: number;
}

// 分页显示模块类型
type PaginationModule = 'pages' | 'size-picker' | 'quick-jumper' | 'info';

// ==================== Props 定义 ====================
interface Props<T = any> {
  columns: TableColumn<T>[];
  data: T[];
  loading?: boolean;
  pagination?: PaginationData;
  showPagination?: boolean;
  showPageSizeSelector?: boolean;
  pageSizeOptions?: number[];
  displayOrder?: PaginationModule[]; // 分页模块显示顺序
  showQuickJumper?: boolean; // 显示快速跳转
  loadingText?: string;
  emptyText?: string;
  getRowKey?: (row: T, index: number) => string | number;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showPagination: true,
  showPageSizeSelector: true,
  pageSizeOptions: () => [10, 20, 50, 100],
  displayOrder: () => ['info', 'size-picker', 'pages'] as PaginationModule[],
  showQuickJumper: false,
  loadingText: "加载中...",
  emptyText: "暂无数据",
  pagination: () => ({ page: 1, limit: 10, total: 0 })
});

// ==================== Emits 定义 ====================
const emits = defineEmits<{
  pageChange: [page: number];
  pageSizeChange: [size: number];
  rowClick: [row: any, index: number];
}>();

// ==================== 状态定义 ====================
const quickJumpValue = ref<string>('');

// ==================== 计算属性 ====================
const totalPages = computed(() => {
  return Math.ceil(props.pagination.total / props.pagination.limit);
});

// ==================== 方法定义 ====================
const formatCellValue = (value: any) => {
  return value ?? "-";
};

const handlePageChange = (page: number) => {
  emits("pageChange", page);
};

const handlePageSizeChange = (value: any) => {
  if (value) {
    emits("pageSizeChange", Number(value));
  }
};

const handleRowClick = (row: any, index: number) => {
  emits("rowClick", row, index);
};

// 快速跳转处理
const handleQuickJump = () => {
  const page = parseInt(quickJumpValue.value);
  if (page && page >= 1 && page <= totalPages.value && page !== props.pagination.page) {
    handlePageChange(page);
  }
  quickJumpValue.value = '';
};

const getEllipsisTooltip = (ellipsis: boolean | { tooltip?: boolean }) => {
  if (typeof ellipsis === "boolean") {
    return ellipsis;
  }
  return ellipsis?.tooltip !== false;
};

const getColumnStyle = (column: any) => {
  if (column.width) {
    return { width: column.width };
  }
  // 没有设置宽度的列，让它们平分剩余空间
  return {};
};
</script>

<style scoped>
/* 分页组件深度样式覆盖 */
:deep([data-slot="pagination"]) {
  justify-content: flex-start !important;
}

:deep([data-slot="pagination-content"]) {
  gap: 0.25rem !important;
}

/* 确保分页按钮样式正确应用 */
:deep([data-slot="pagination-item"]) {
  min-width: 2rem !important;
  height: 2rem !important;
  padding: 0 !important;
  font-size: 0.75rem !important;
  border-radius: 0.375rem !important;
}

/* 省略号样式 */
:deep([data-type="ellipsis"]) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 2rem !important;
  height: 2rem !important;
}

/* 确保表头在 ScrollArea 中正确固定 */
:deep([data-radix-scroll-area-viewport] thead) {
  position: sticky !important;
  top: 0 !important;
  z-index: 10 !important;
}
</style>
