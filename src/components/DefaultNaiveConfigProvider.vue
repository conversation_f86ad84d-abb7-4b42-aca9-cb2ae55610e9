<template>
  <n-config-provider ref="configProvider" :theme-overrides="themeOverrides">
    <slot></slot>
  </n-config-provider>
</template>

<script setup lang="ts">
import { NConfigProvider } from "naive-ui";
import type { GlobalThemeOverrides } from "naive-ui";

const themeOverrides: GlobalThemeOverrides = {
  Tabs: {
    tabTextColorBar: "rgba(255, 255 ,255, 0.9)",
    tabTextColorHoverBar: "#18a058",
    tabTextColorActiveBar: "#18a058",
    barColor: "#18a058"
  },
  Switch: {
    buttonColor: "#fff",
    railColorActive: "#18a058",
    railColor: "rgba(0, 0, 0, 0.14)"
  },
  DataTable: {
    thColor: "rgba(250, 250, 252, 1)",
    thTextColor: "rgb(31, 34, 37)",
    tdColor: "#fff",
    tdTextColor: "rgb(51, 54, 57)",
    tdColorHover: "rgba(247, 247, 250, 1)",
    borderColor: "rgba(239, 239, 245, 1)"
  },
  Pagination: {
    buttonColor: "#0000",
    buttonBorder: "1px solid rgb(224, 224, 230)",
    buttonBorderHover: "1px solid rgb(224, 224, 230)",
    itemTextColorHover: "#36ad6a",
    itemTextColor: "rgb(51, 54, 57)",
    itemColorActive: "#0000",
    itemBorderActive: "1px solid #18a058",
    itemTextColorActive: "#18a058",
    itemBorderDisabled: "1px solid rgb(224, 224, 230)",
    itemTextColorDisabled: "rgba(194, 194, 194, 1)",
    itemColorDisabled: "rgb(250, 250, 252)",
    buttonBorderPressed: "1px solid rgb(224, 224, 230)",
    buttonIconColor: "rgb(51, 54, 57)",
    buttonIconColorHover: "rgb(51, 54, 57)",
    jumperTextColor: "rgba(255, 255, 255, 0.82)"
  },
  Button: {
    textColor: "rgb(51, 54, 57)",
    border: "1px solid rgb(224, 224, 230)",
    borderHover: "1px solid #36ad6a",
    textColorHover: "#36ad6a",
    textColorPressed: "#0c7a43",
    textColorFocus: "#36ad6a",
    borderPressed: "1px solid #0c7a43",
    borderFocus: "1px solid #36ad6a"
  },
  Radio: {
    textColor: "rgb(51, 54, 57)"
  },
  Card: {
    borderColor: "rgba(255, 255, 255, 0.09)",
    colorModal: "rgb(44, 44, 50)",
    titleTextColor: "rgba(255, 255, 255, 0.9)",
    closeIconColor: "rgba(255, 255, 255, 0.52)",
    closeIconColorHover: "rgba(255, 255, 255, 0.52)"
  }
};
</script>

<style scoped></style>
