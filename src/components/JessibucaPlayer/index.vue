<template>
  <div class="aspect-video w-full">
    <div ref="playerRef"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeMount } from "vue";
import decoder from "@/assets/js/jessibuca/decoder.js?url";

const playerRef = ref<HTMLDivElement>();

// jessibuca player
const jessibucaPlayer = ref<Jessibuca>();

interface Props {
  /** 播放地址 */
  wsFlv?: string;
  wssFlv?: string;
  wsRaw?: string;
  config: Partial<Jessibuca.Config>;
}

const props = withDefaults(defineProps<Props>(), {});

// 创建播放器
const createJessibucaPlayer = () => {
  jessibucaPlayer.value = new Jessibuca({
    container: playerRef.value!,
    ...props.config,
    decoder: decoder
  });
  jessibucaPlayer.value.play("ws://*************:18181/tv");
};

// 销毁
const destroy = () => {
  jessibucaPlayer.value?.destroy();
};

onMounted(() => {
  createJessibucaPlayer();
});

onBeforeMount(() => {
  jessibucaPlayer.value && jessibucaPlayer.value.destroy();
  jessibucaPlayer.value = null as unknown as undefined;
});

defineExpose({
  destroy
});
</script>

<style scoped lang="scss"></style>
