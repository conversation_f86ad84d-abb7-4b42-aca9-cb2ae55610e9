<template>
  <div
    :class="
      clsx(
        'inline-flex divide-border rounded-md border',
        orientation === 'horizontal' ? 'flex-row divide-x' : 'flex-col divide-y'
      )
    "
  >
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { clsx } from "clsx";

interface Props {
  orientation?: "horizontal" | "vertical";
}

withDefaults(defineProps<Props>(), {
  orientation: "horizontal"
});
</script>

<style scoped>
/* 确保按钮之间的分割线和圆角正确显示 */
:where(.flex-row) {
  @apply border-r-0 last:border-r;
}

:where(.flex-col) {
  @apply border-b-0 last:border-b;
}

:where(.flex-row) > :not(:last-child):not(:first-child) {
  @apply rounded-none;
}

:where(.flex-col) > :not(:last-child):not(:first-child) {
  @apply rounded-none;
}

:where(.flex-row) > :first-child {
  @apply rounded-r-none;
}

:where(.flex-row) > :last-child {
  @apply rounded-l-none;
}

:where(.flex-col) > :first-child {
  @apply rounded-b-none;
}

:where(.flex-col) > :last-child {
  @apply rounded-t-none;
}
</style>
