<template>
  <div class="player-container h-hull w-full">
    <video class="video-js" ref="videoPlayer"></video>
  </div>
</template>

<script setup lang="ts">
import { useTemplateRef, ref, onMounted, onUnmounted, watch } from "vue";
import videojs from "video.js";
import "flv.js";
import "videojs-flvjs-es6";
import "video.js/dist/video-js.css";

interface Props {
  src: string;
  autoplay?: boolean;
  muted?: boolean;
  controls?: boolean;
  isLive?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  src: "",
  autoplay: true,
  muted: true,
  controls: true,
  isLive: true
});

const videoPlayerRef = useTemplateRef("videoPlayer");
const videoPlayer = ref<videojs.Player | null>(null);

const initPlayer = () => {
  if (!videoPlayer.value) return;
  if (!props.src) return;
  const options: videojs.PlayerOptions = {
    autoplay: props.autoplay,
    muted: props.muted,
    controls: props.controls,
    fluid: true,
    techOrder: ["html5", "flvjs"],
    sources: [
      {
        src: props.src,
        type: "video/x-flv"
      }
    ]
  };

  try {
    // 初始化播放器
    videoPlayer.value = videojs(videoPlayerRef.value!, options, () => {
      console.log("FLV播放器已初始化");
      //   setupPlayerEvents();
    });
  } catch (err) {
    console.error("播放器初始化错误:", err);
  }
};

onMounted(() => {
  initPlayer();
});

watch(
  () => props.src,
  newSrc => {
    if (videoPlayer.value && newSrc) {
      initPlayer();
      // videoPlayer.value.src({ type: "video/x-flv", src: newSrc });
      // videoPlayer.value.ready(() => {
      //   console.log("监听src更换 FLV播放器已就绪");
      // });
    }
  }
);

onUnmounted(() => {
  videoPlayer.value && videoPlayer.value.dispose();
  videoPlayer.value = null;
});
</script>
