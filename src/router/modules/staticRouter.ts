import type { RouteRecordRaw } from "vue-router";

import { RoutePath } from "@/constants";

export const staticRouter: Array<RouteRecordRaw> = [
  {
    path: "/",
    redirect: RoutePath.HOME
  },
  {
    path: "/layout",
    name: "layout",
    component: () => import("@/layout/index.vue"),
    children: []
  },
  {
    path: RoutePath.HOME,
    name: RoutePath.HOME,
    component: () => import("@/views/home/<USER>")
  },
  {
    path: RoutePath.LOGIN,
    name: RoutePath.LOGIN,
    component: () => import("@/views/login/index.vue")
  }
];

export const errorRouter: Array<RouteRecordRaw> = [
  {
    path: RoutePath.NOT_FOUND,
    name: RoutePath.NOT_FOUND,
    component: () => import("@/views/errorPage/notFound.vue"),
    meta: {
      title: "not found"
    }
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import("@/views/errorPage/notFound.vue")
  }
];
