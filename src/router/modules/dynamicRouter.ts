import type { RouteRecordRaw } from "vue-router";

import router from "@/router";

import { useAuthStore } from "@/store/modules/auth";

const modules = import.meta.glob("@/views/**/*.vue");

export const initDynamicRouter = async () => {
  const authStore = useAuthStore();

  await authStore.getMenuList();

  authStore.flatMenuListGet.forEach(item => {
    if (item.children) {
      delete item.children;
    }
    if (item.component && typeof item.component === "string") {
      item.component = modules["/src/views" + item.component + ".vue"];
    }
    if (item.meta.isFull) {
      router.addRoute(item as unknown as RouteRecordRaw);
    } else {
      router.addRoute("layout", item as unknown as RouteRecordRaw);
    }
  });
};
