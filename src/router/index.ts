import { createRouter, createWeb<PERSON>ash<PERSON>ist<PERSON>, createWeb<PERSON>istory } from "vue-router";

import { initDynamicRouter } from "./modules/dynamicRouter";
import { staticRouter, errorRouter } from "./modules/staticRouter";

import { useAuthStore } from "@/store/modules/auth";
import { useGlobalStore } from "@/store/modules/global";
import { RoutePath } from "@/constants";

const mode = import.meta.env.VITE_ROUTER_MODE;

const routerMode = {
  hash: () => createWebHashHistory(),
  history: () => createWebHistory()
};

const router = createRouter({
  history: routerMode[mode as keyof typeof routerMode](),
  routes: [...staticRouter, ...errorRouter],
  strict: false,
  scrollBehavior: () => ({ top: 0, left: 0 })
});

router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore();
  const globalStore = useGlobalStore();

  const title = import.meta.env.VITE_GLOBAL_APP_TITLE;
  document.title = to.meta.title ? `${to.meta.title} - ${title}` : title;

  if (to.path.toLocaleLowerCase() === RoutePath.LOGIN) {
    if (authStore.token) return next(from.fullPath);
    resetRouter();
    return next();
  }

  if (!authStore.token) return next({ path: RoutePath.LOGIN, replace: true });

  if (!authStore.authMenuListGet.length) {
    await initDynamicRouter();
    return next({ ...to, replace: true });
  }

  if (authStore.token) {
    globalStore.getDictDataList();
    globalStore.getRobotList();
  }

  next();
});

const resetRouter = () => {
  const authStore = useAuthStore();
  authStore.flatMenuListGet.forEach(route => {
    const { name } = route;
    if (name && router.hasRoute(name)) router.removeRoute(name);
  });
};

export default router;
