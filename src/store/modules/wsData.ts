import { defineStore } from "pinia";

import type { WsDataState, RealtimeInfoList } from "@/store/interface";

type VehicleBodyTag = "speed";

type ChargingRoomTag = "battery";

const initialRobotWeatherData: WsDataState["robotWeatherData"] = { temperature: 0, humidity: 0, windspeed: 0 };

const initialRobotRealtimeInfo: WsDataState["robotRealtimeInfo"] = {
  speed: 0,
  orientation: "",
  posX: "",
  posY: "",
  ptzHor: 0,
  ptzVer: 0,
  batteryPercent: 0,
  chargeVoltage: 0,
  chargeCurr: 0
};

const generateVehicleBodyList = (props?: {
  speed: number;
  orientation: string;
  posX: string;
  posY: string;
}): RealtimeInfoList<VehicleBodyTag>[] => {
  return [
    { id: 1, label: "时间", value: "" },
    { id: 2, label: "IP", value: "" },
    { id: 3, label: "速度", value: props?.speed ?? "", tag: "speed" },
    { id: 4, label: "方向", value: props?.orientation ?? "" },
    { id: 5, label: "X轴", value: props?.posX ?? "" },
    { id: 6, label: "Y轴", value: props?.posY ?? "" },
    { id: 7, label: "里程计", value: "" }
  ];
};

const generatePTZInformationList = (props?: { ptzHor: number; ptzVer: number }): WsDataState["ptzInformationList"] => {
  return [
    { id: 1, label: "水平转向", value: props?.ptzHor ?? "" },
    { id: 2, label: "垂直转向", value: props?.ptzVer ?? "" },
    { id: 3, label: "可见光变焦", value: "" },
    { id: 4, label: "红外光聚焦", value: "" }
  ];
};

const generateChargingRoomList = (props?: {
  batteryPercent: number;
  chargeVoltage: number;
  chargeCurr: number;
}): RealtimeInfoList<ChargingRoomTag>[] => {
  return [
    { id: 1, label: "电量", value: props?.batteryPercent ?? "", tag: "battery" },
    { id: 2, label: "电压", value: props?.chargeVoltage ?? "" },
    { id: 3, label: "电流", value: props?.chargeCurr ?? "" },
    { id: 4, label: "心跳时间", value: "" },
    { id: 5, label: "充电房门", value: "" },
    { id: 6, label: "在充电房", value: "" },
    { id: 7, label: "充电头接触", value: "" },
    { id: 8, label: "充电桩带电", value: "" }
  ];
};

export const useWsDataStore = defineStore("itl-robot-dog-ws-data", {
  state: (): WsDataState<VehicleBodyTag, ChargingRoomTag> => ({
    robotWeatherData: initialRobotWeatherData,
    robotRealtimeInfo: initialRobotRealtimeInfo,
    vehicleBodyList: generateVehicleBodyList(),
    ptzInformationList: generatePTZInformationList(),
    chargingRoomList: generateChargingRoomList()
  }),
  actions: {
    setRobotWeatherData(data: WsDataState["robotWeatherData"]) {
      this.robotWeatherData = data;
    },
    setRobotRealtimeInfo(data: WsDataState["robotRealtimeInfo"]) {
      this.robotRealtimeInfo = data;
      this.vehicleBodyList = generateVehicleBodyList({
        speed: data.speed,
        orientation: data.orientation,
        posX: data.posX,
        posY: data.posY
      });

      this.ptzInformationList = generatePTZInformationList({
        ptzHor: data.ptzHor,
        ptzVer: data.ptzVer
      });

      this.chargingRoomList = generateChargingRoomList({
        batteryPercent: data.batteryPercent,
        chargeVoltage: data.chargeVoltage,
        chargeCurr: data.chargeCurr
      });
    }
  }
});
