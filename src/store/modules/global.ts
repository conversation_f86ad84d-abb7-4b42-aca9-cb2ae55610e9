import { defineStore } from "pinia";

import type { GlobalState } from "@/store/interface";

import { robotStatusApi } from "@/api/modules/visual";
import { dictDataApi, robotListApi } from "@/api/modules/robot";

const initialRobotStatue: GlobalState["robotStatus"] = {
  substation: "",
  robotSn: "",
  runStatus: "",
  panStatus: "",
  naviStatus: "",
  regnStatus: "",
  mode: "",
  speed: 0,
  runtime: 0,
  mileage: 0,
  posX: "",
  posY: "",
  posZ: "",
  orientation: "",
  posLng: "",
  posLat: "",
  batteryPercent: 0,
  chargeCurr: 0,
  chargeVoltage: 0,
  devTemp: 0,
  ptzHor: 0,
  ptzVer: 0,
  batteryTemp: 0,
  isObstacle: "",
  temperature: 0,
  humidity: 0,
  windspeed: 0,
  wiper: "",
  light: "",
  obstacle: "",
  cameraMultiple: 0,
  time: ""
};

/**
 * 字典数据类型id
 * 1 告警级别
 * 2 任务类型
 * 3 任务优先级
 * 4 审核状态
 */
type DictTypeId = 1 | 2 | 3 | 4;

export const useGlobalStore = defineStore("itl-robot-dog-global", {
  state: (): GlobalState => ({
    robotStatus: initialRobotStatue,
    dictDataList: [],
    robotList: []
  }),
  actions: {
    async getRobotStatus() {
      const res = await robotStatusApi();
      this.$state.robotStatus = res.data;
    },
    async getRobotList() {
      const res = await robotListApi();
      this.$state.robotList = res.data;
    },
    async getDictDataList() {
      const res = await dictDataApi();
      this.$state.dictDataList = res.data;
    },
    getDictDataByTypeId(typeId: DictTypeId) {
      if (!typeId) return [];
      return this.$state.dictDataList
        .filter(v => v.dictTypeId === typeId)
        .map(v => ({ label: v.dictLabel, value: v.dictValue, remark: v.remark }));
    },
    findDictDataItemInfo(typeId: DictTypeId, value: number) {
      const result = this.getDictDataByTypeId(typeId);
      // console.log(result);

      const item = result.find(v => v.value === value);
      return item || { label: "", value: "" };
    },
    findDictDataItemInfoToLevel(typeId: DictTypeId, value: string) {
      const result = this.getDictDataByTypeId(typeId);
      // console.log(result);

      const item = result.find(v => v.remark === value);
      return item || { label: "", value: "" };
    },
    findRobotInfo(id: number) {
      const item = this.$state.robotList.find(v => v.id === id);
      return item?.robotName || "";
    },
    clear() {
      this.$state.dictDataList = [];
      this.$state.robotList = [];
    }
  },
  persist: {
    pick: ["dictDataList", "robotList"]
  }
});
