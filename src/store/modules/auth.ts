import { defineStore } from "pinia";

import { menuApi } from "@/api/modules/user";

import type { AuthState } from "@/store/interface";

import { getFlatMenuList, getShowMenuList } from "@/utils";

const initialRole: AuthState["role"] = {
  authority: "",
  id: 0,
  name: ""
};

export const useAuthStore = defineStore("itl-robot-dog-auth", {
  state: (): AuthState => ({
    token: "",
    menuList: [],
    role: initialRole
  }),
  getters: {
    authMenuListGet: state => state.menuList,
    flatMenuListGet: state => getFlatMenuList(state.menuList),
    showMenuListGet: state => getShowMenuList(state.menuList)
  },
  actions: {
    setToken(token: string) {
      this.token = token;
    },
    setRole(role: AuthState["role"]) {
      this.role = role;
    },
    async getMenuList() {
      const { data } = await menuApi();
      this.menuList = data as Menu.MenuOptions[];
    },
    clear() {
      this.$state.role = { ...initialRole };
      this.$state.token = ""
    }
  },
  persist: {
    pick: ["token", "role"]
  }
});
