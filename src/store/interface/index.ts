import type { RobotStatusRes } from "@/api/interface/visual";
import type { LoginResultRole } from "@/api/interface/user";
import type { DictDataResult, RobotListResult } from "@/api/interface/robot";

export interface AuthState {
  token: string;
  menuList: Menu.MenuOptions[];
  role: LoginResultRole;
}

export interface RequestState {
  loading: boolean;
}

export interface GlobalState {
  robotStatus: RobotStatusRes;
  dictDataList: DictDataResult[];
  robotList: RobotListResult[];
}

interface RobotWeatherData {
  /** 温度 */
  temperature: number;
  /** 湿度 */
  humidity: number;
  /** 风力 */
  windspeed: number;
}

interface RobotRealtimeInfo {
  /** 速度 */
  speed: number;
  /** 方向 */
  orientation: string;
  /** 方向X */
  posX: string;
  /** 方向Y */
  posY: string;
  /** 云台水平角度 */
  ptzHor: number;
  /** 云台垂直角度 */
  ptzVer: number;
  /** 当前电量百分比 */
  batteryPercent: number;
  /** 充电电压 */
  chargeVoltage: number;
  /** 充电电流 */
  chargeCurr: number;
}

export interface RealtimeInfoList<T = string> {
  id: number;
  label: string;
  value: string | number;
  tag?: T;
}

export interface WsDataState<RealtimeInfoListT = any, ChargingRoomT = any> {
  robotWeatherData: RobotWeatherData;
  robotRealtimeInfo: RobotRealtimeInfo;
  vehicleBodyList: RealtimeInfoList<RealtimeInfoListT>[];
  ptzInformationList: RealtimeInfoList[];
  chargingRoomList: RealtimeInfoList<ChargingRoomT>[];
}
