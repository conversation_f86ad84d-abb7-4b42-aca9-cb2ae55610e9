<template>
  <main class="scrollable flex h-full flex-col bg-[#EBF2FF]">
    <!-- header -->
    <header class="grid h-25 grid-cols-[20rem_1fr_20rem] items-center bg-white px-2">
      <h1 class="text-2xl font-bold">
        <a href="/">{{ title }}</a>
      </h1>
      <div>
        <Menu></Menu>
      </div>
    </header>
    <!-- content -->
    <div class="flex-1 overflow-hidden p-2">
      <router-view></router-view>
    </div>
  </main>
</template>

<script setup lang="ts">
import Menu from "./components/Menu.vue";

const title = import.meta.env.VITE_GLOBAL_APP_TITLE;
</script>
