<template>
  <navigation-menu>
    <navigation-menu-list class="gap-4">
      <navigation-menu-link as-child>
        <RouterLink :to="RoutePath.HOME">首页</RouterLink>
      </navigation-menu-link>
      <navigation-menu-item v-for="item in menuList" :key="item.path" class="text-lg">
        <template v-if="item.children?.length">
          <navigation-menu-trigger>
            {{ item.meta.title }}
          </navigation-menu-trigger>
          <navigation-menu-content>
            <ul class="grid w-100 grid-cols-2 gap-3">
              <li v-for="child in item.children" :key="child.path">
                <navigation-menu-link as-child>
                  <router-link :to="child.path">{{ child.meta.title }}</router-link>
                </navigation-menu-link>
              </li>
            </ul>
          </navigation-menu-content>
        </template>
        <template v-else>
          <navigation-menu-link as-child :class="navigationMenuTriggerStyle()">
            <router-link :to="item.path">{{ item.meta.title }}</router-link>
          </navigation-menu-link>
        </template>
      </navigation-menu-item>
    </navigation-menu-list>
  </navigation-menu>
</template>

<script setup lang="ts">
// ==================== 导入依赖 ====================
import { computed } from "vue";
import { RouterLink } from "vue-router";
// ==================== 页面组件导入 ====================

// ==================== 全局组件导入 ====================
import {
  NavigationMenu,
  NavigationMenuList,
  NavigationMenuItem,
  NavigationMenuTrigger,
  NavigationMenuContent,
  NavigationMenuLink,
  navigationMenuTriggerStyle
} from "@/components/ui/navigation-menu";

// ==================== 其他导入项 ====================
import { RoutePath } from "@/constants";
import { useAuthStore } from "@/store/modules/auth";
// ==================== TypeScript ====================

// ==================== vue宏定义 ====================

// ==================== 钩子定义 ====================
const authStore = useAuthStore();
// ==================== 状态定义 ====================

const menuList = computed(() => authStore.showMenuListGet);
// ==================== 方法定义 ====================

// ==================== 生命周期 ====================
</script>
