<template>
  <header class="grid h-25 grid-cols-[20rem_1fr_20rem] items-center bg-white px-2">
    <h1 class="text-2xl font-bold">
      <Link to="">{{ title }}</Link>
    </h1>
    <div class="self-end justify-self-center">
      <n-menu responsive mode="horizontal" :options="menulist"></n-menu>
    </div>
    <div></div>
  </header>
</template>

<script setup lang="ts">
import { h } from "vue";
import { type MenuOption } from "naive-ui";
import { RouterLink } from "vue-router";

import Link from "@/components/Link/index.vue";

import { useAuthStore } from "@/store/modules/auth";

const title = import.meta.env.VITE_GLOBAL_APP_TITLE;

const authStore = useAuthStore();

const menulist: MenuOption[] = authStore.showMenuListGet.map(v => {
  return {
    key: v.path,
    label: () => h(RouterLink, { to: v.path }, { default: () => v.meta.title }),
    children: v.children?.map(child => {
      return {
        key: child.path,
        label: () => h(RouterLink, { to: child.path }, { default: () => child.meta.title })
      } satisfies MenuOption;
    })
  };
});
</script>
