<template>
  <div class="flex h-full gap-x-4 overflow-hidden">
    <Card class="h-full overflow-hidden w-[40%]">
      <CardHeader class="border-b">
        <p>任务日期</p>
      </CardHeader>
      <CardContent class="h-full">
        <n-calendar class="h-full!" @update:value="handleUpdateCalendarValue" />
      </CardContent>
    </Card>
    <!-- 任务列表 -->
    <Card class="h-full overflow-hidden w-[60%]">
      <CardHeader class="border-b">
        <p>任务列表</p>
      </CardHeader>
      <CardContent class="flex h-full flex-col overflow-hidden">
        <!-- 查询 -->
        <div class="space-y-2">
          <!-- 执行状态 -->
          <div class="flex items-center">
            <span>执行状态：</span>
            <n-checkbox-group v-model:value="searchParam.status">
              <n-checkbox v-for="item in taskExecutionStatus" :key="item.value" :value="item.value">{{ item.label }}</n-checkbox>
            </n-checkbox-group>
          </div>
          <div>
            <n-form inline label-placement="left">
              <n-form-item label="开始时间：">
                <n-date-picker clearable placeholder="选择日期"></n-date-picker>
              </n-form-item>
              <n-form-item label="结束时间：">
                <n-date-picker clearable placeholder="选择日期"></n-date-picker>
              </n-form-item>
              <n-form-item label="任务名称：">
                <n-input clearable></n-input>
              </n-form-item>
              <n-form-item>
                <ButtonGroup>
                  <Button @click="search">搜索</Button>
                  <Button @click="search" variant="warning">停止</Button>
                  <Button @click="search" variant="destructive">删除</Button>
                </ButtonGroup>
              </n-form-item>
            </n-form>
          </div>
        </div>
        <!-- 列表 -->
        <n-data-table
          :columns="columns"
          :data="tableData"
          :row-key="rowData => rowData.id"
          flex-height
          style="height: 100%"
        ></n-data-table>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { h, onMounted } from "vue";
import { type DataTableColumn, NButton } from "naive-ui";
import dayjs from "dayjs";

import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { Button, ButtonGroup } from "@/components/ui/button";

import { taskExecutionStatus, TASK_EXECUTION_STATUS_MAP } from "@/dictionary";

import { getDailyTaskListApi } from "@/api/modules/mock";

import { useTable } from "@/hooks";

interface RowData {
  id: number;
  name: string;
  robot: string;
  startTime: string;
  endTime: string;
  status: number;
  total: number;
  doneTotal: number;
  warningTotal: number;
}

const { getTableList, tableData, search, searchParam } = useTable(getDailyTaskListApi, {}, true, undefined, undefined, params => {
  params.status = params.status?.join(",");

  return params;
});

const columns: DataTableColumn<RowData>[] = [
  {
    type: "selection"
  },
  {
    key: "name",
    title: "任务名称",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: "robot",
    title: "机器人",
    align: "center"
  },
  {
    key: "startTime",
    title: "开始时间",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: "endTime",
    title: "结束时间",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: "status",
    title: "任务状态",
    align: "center",
    render: rowData => TASK_EXECUTION_STATUS_MAP[rowData.status]
  },
  {
    key: "total",
    title: "任务总点数",
    align: "center"
  },
  {
    key: "doneTotal",
    title: "完成任务点数",
    align: "center"
  },
  {
    key: "warningTotal",
    title: "警告总数",
    align: "center"
  },
  {
    key: "action",
    title: "操作",
    render() {
      return h(NButton, { type: "warning", ghost: true }, { default: () => "导出" });
    }
  }
];

const handleUpdateCalendarValue = (timestamp: number, info: { year: number; month: number; date: number }) => {
  console.log(timestamp, info);
  searchParam.value.taskDate = dayjs(timestamp).format("YYYY-MM-DD");
  search();
};

onMounted(() => {
  getTableList();
});
</script>
