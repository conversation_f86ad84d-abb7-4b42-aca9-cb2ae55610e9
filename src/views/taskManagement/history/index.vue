<template>
  <div class="grid h-full grid-cols-2 gap-x-5">
    <Card class="overflow-hidden">
      <CardContent class="flex h-full flex-col">
        <!-- Tab选项卡在上方 -->
        <div class="mb-2">
          <Tabs default-value="device">
            <TabsList class="px-3 py-5">
              <TabsTrigger class="py-3" value="device" @click="queryKeyword = 'device'">设备查询</TabsTrigger>
              <TabsTrigger class="py-3" value="task" @click="queryKeyword = 'task'">任务查询</TabsTrigger>
              <TabsTrigger class="py-3" value="detail" @click="queryKeyword = 'detail'">详细查询</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        <!-- 搜索查询表单 -->
        <div class="mb-0">
          <n-form inline label-placement="left">
            <n-form-item label="日期">
              <n-date-picker class="w-65" type="daterange"></n-date-picker>
            </n-form-item>
            <n-form-item label="部件">
              <n-input class="mr-2 w-25!"></n-input>
            </n-form-item>
            <n-form-item>
              <n-select class="w-30!" placeholder="告警状态" :options="warningList"></n-select>
            </n-form-item>
            <n-form-item>
              <ButtonGroup>
                <Button>查询</Button>
                <Button>导出</Button>
                <Button>设置列</Button>
              </ButtonGroup>
            </n-form-item>
          </n-form>
        </div>

        <!-- 内容区域左右布局 -->
        <div class="grid flex-1 grid-cols-[1.5fr_3.5fr] gap-x-4 overflow-hidden" v-if="queryKeyword !== 'detail'">
          <!-- 左侧DeviceQuery组件，根据tab切换显示不同内容 -->
          <div class="overflow-hidden">
            <div v-if="queryKeyword === 'device'">
              <DeviceQuery />
            </div>
            <div v-if="queryKeyword === 'task'">
              <!-- 任务查询内容 -->
              <TaskQuery />
            </div>
          </div>

          <!-- 右侧表格，不随tab切换而改变 -->
          <div class="overflow-hidden" v-if="queryKeyword !== 'detail'">
            <n-data-table
              striped
              :data="tableData"
              :columns="columns"
              flex-width
              style="width: 100%"
              :pagination="{
                showSizePicker: true,
                showQuickJumper: true
              }"
            ></n-data-table>
          </div>
        </div>
        <div v-if="queryKeyword === 'detail'">
          <!-- 详细查询内容 -->
          <div class="overflow-hidden" v-if="queryKeyword == 'detail'">
            <n-data-table
              striped
              :data="tableData"
              :columns="columns"
              flex-width
              style="width: 100%"
              :pagination="{
                showSizePicker: true,
                showQuickJumper: true
              }"
            ></n-data-table>
          </div>
        </div>
      </CardContent>
    </Card>
    <Card>
      <CardHeader>
        <Tabs default-value="basic">
          <TabsList class="h-auto w-full p-0 px-2 py-2">
            <TabsTrigger value="basic" class="flex flex-col p-0">
              <AlignRightIcon class="size-10" />
              基本信息
            </TabsTrigger>
            <TabsTrigger value="image" class="flex flex-col p-0">
              <ChartPieIcon class="size-10" />
              图片信息
            </TabsTrigger>
            <TabsTrigger value="videos" class="flex flex-col p-0">
              <MonitorCog class="size-10" />
              历史视频
            </TabsTrigger>
            <TabsTrigger value="curve" class="flex flex-col p-0">
              <ChartLine class="size-10" />
              历史曲线
            </TabsTrigger>
          </TabsList>
          <TabsContent value="basic" class="overflow-hidden">
            <BasicInfo />
          </TabsContent>
          <TabsContent value="image" class="overflow-hidden">
            <ImgAnalysis />
          </TabsContent>
          <TabsContent value="videos" class="overflow-hidden">
            <Video />
          </TabsContent>
          <TabsContent value="curve" class="overflow-hidden">
            <HistoryCurve />
          </TabsContent>
        </Tabs>
      </CardHeader>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { AlignRightIcon, ChartPieIcon, MonitorCog, ChartLine } from "lucide-vue-next";
import { type DataTableRowData, type DataTableColumn } from "naive-ui";
import { ButtonGroup, Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import DeviceQuery from "./components/DeviceQuery.vue";
import TaskQuery from "./components/TaskQuery.vue";
import BasicInfo from "./components/BasicInfo.vue";
import ImgAnalysis from "./components/ImgAnalysis.vue";
import Video from "./components/Video.vue";
import HistoryCurve from "./components/HistoryCurve.vue";

const queryKeyword = ref("device");

const warningList = [
  {
    label: "正常",
    value: 1
  },
  {
    label: "一般警告",
    value: 2
  },
  {
    label: "预警",
    value: 3
  },
  {
    label: "严重警告",
    value: 4
  },
  {
    label: "危急警告",
    value: 5
  },
  {
    label: "未知",
    value: 6
  }
];

const tableData: DataTableRowData[] = Array.from({ length: 20 }).fill({
  partName: "A1设备 - A1设备开关",
  detection: 1,
  status: 1,
  inspectTime: "2025-02-29 15:20:20"
}) as DataTableRowData[];

const columns: DataTableColumn[] = [
  {
    key: "inspectionTime",
    title: "名称(设备名-部件名)",
    align: "center"
  },
  {
    key: "inspectionValue",
    title: "检测值",
    align: "center"
  },
  {
    key: "status",
    title: "状态",
    align: "center"
  },
  {
    key: "inspectionValue",
    title: "巡检时间",
    align: "center"
  },
  {
    key: "inspectionValue",
    title: "执行状态",
    align: "center"
  }
];
</script>
