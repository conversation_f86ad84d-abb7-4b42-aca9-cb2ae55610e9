<!-- 任务查询 -->
<template>
  <div class="flex h-full flex-col">
    <div>
      <n-form inline label-placement="left">
        <n-form-item label="任务名称">
          <n-input class="w-35!"></n-input>
        </n-form-item>
        <n-form-item>
          <Button class="px-2">查询</Button>
        </n-form-item>
      </n-form>

      <n-data-table
        :data="data"
        :columns="columns"
        default-expand-all
        class="h-full"
        :pagination="{
          showSizePicker: true,
          showQuickJumper: true
        }"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Button } from "@/components/ui/button";
import { type DataTableRowData, type DataTableColumn } from "naive-ui";

const data: DataTableRowData[] = [];

const columns: DataTableColumn[] = [
  {
    key: "inspectionTime",
    title: "巡检任务",
    align: "center"
  }
];
</script>
