<!-- 图片分析 -->
<template>
  <div class="flex h-full flex-col">
    <div class="h-full w-full p-0 px-2 py-2">
      <Tabs default-value="visibleLight">
        <TabsList class="px-3 py-5">
          <TabsTrigger value="visibleLight" class="py-3"> 可见光管理 </TabsTrigger>
          <TabsTrigger value="infrared" class="py-3"> 红外光管理 </TabsTrigger>
        </TabsList>
        <TabsContent value="visibleLight" class="flex justify-center">
          <n-image-group show-toolbar-tooltip>
            <n-space>
              <n-image
                style="max-height: 25vh; max-width: 20vw"
                src="https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg"
              />
            </n-space>
          </n-image-group>
        </TabsContent>
        <TabsContent value="infrared" class="flex justify-center">
          <n-image-group show-toolbar-tooltip>
            <n-space>
              <n-image
                style="max-height: 25vh; max-width: 20vw"
                src="https://naive-ui.oss-cn-beijing.aliyuncs.com/carousel-img/carousel2.jpeg"
              />
            </n-space>
          </n-image-group>
        </TabsContent>
      </Tabs>
      <n-form label-placement="top">
        <n-form-item label="分析结果">
          <n-input type="textarea" />
        </n-form-item>
      </n-form>
      <n-divider dashed> </n-divider>
      <div>
        <p class="font-bold">人工分析</p>
        <n-form inline label-placement="left" class="h-10">
          <n-form-item label="巡检值">
            <n-input class="w-35!"></n-input>
          </n-form-item>
          <n-form-item>
            <n-select class="w-30!" placeholder="告警状态" :options="warningList"></n-select>
          </n-form-item>
          <n-form-item>
            <Button class="px-2">审核</Button>
          </n-form-item>
        </n-form>
        <n-input type="textarea" placeholder="请输入新内容" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";

const warningList = [
  {
    label: "正常",
    value: 1
  },
  {
    label: "一般警告",
    value: 2
  },
  {
    label: "预警",
    value: 3
  },
  {
    label: "严重警告",
    value: 4
  },
  {
    label: "危急警告",
    value: 5
  },
  {
    label: "未知",
    value: 6
  }
];
</script>
