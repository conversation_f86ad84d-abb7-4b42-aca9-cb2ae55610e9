<!-- 历史曲线 -->
<template>
  <div class="flex h-full flex-col">
    <n-form inline label-placement="left">
      <n-form-item label="日期">
        <n-date-picker class="w-65" type="daterange"></n-date-picker>
      </n-form-item>

      <Button>查询</Button>
    </n-form>
    <div id="main" class="w-full" style="height: 540px"></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from "vue";
import { CanvasRenderer } from "echarts/renderers";
import { Button } from "@/components/ui/button";
import { LineChart, type LineSeriesOption } from "echarts/charts";
import { UniversalTransition, AxisBreak } from "echarts/features";
import * as echarts from "echarts/core";
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  type TitleComponentOption,
  type TooltipComponentOption,
  type GridComponentOption,
  type DataZoomComponentOption
} from "echarts/components";

// ✅ 这里确保 use 语句结束有分号
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LineChart,
  CanvasRenderer,
  UniversalTransition,
  AxisBreak
]);

// 类型声明
type EChartsOption = echarts.ComposeOption<
  TitleComponentOption | TooltipComponentOption | GridComponentOption | DataZoomComponentOption | LineSeriesOption
>;

let myChart: echarts.ECharts;
let option: EChartsOption;

const formatTime = echarts.time.format;
const _data = generateData1();

option = {
  useUTC: true,
  title: {
    text: "历史曲线",
    left: "left"
  },
  tooltip: {
    show: true,
    trigger: "axis"
  },
  xAxis: [
    {
      type: "time",
      interval: 1000 * 60 * 30,
      axisLabel: {
        showMinLabel: true,
        showMaxLabel: true,
        formatter: (value, index, extra) => {
          if (!extra || !extra.break) {
            return formatTime(value, "{HH}:{mm}", true);
          }
          if (extra.break.type === "start") {
            return formatTime(extra.break.start, "{HH}:{mm}", true) + "/" + formatTime(extra.break.end, "{HH}:{mm}", true);
          }
          return "";
        }
      },
      breakLabelLayout: { moveOverlap: false },
      breaks: [
        {
          start: _data.breakStart,
          end: _data.breakEnd,
          gap: 0
        }
      ],
      breakArea: {
        expandOnClick: false,
        zigzagAmplitude: 0,
        zigzagZ: 200
      }
    }
  ],
  yAxis: { type: "value", min: "dataMin" },
  dataZoom: [
    { type: "inside", xAxisIndex: 0 },
    { type: "slider", xAxisIndex: 0 }
  ],
  series: [
    {
      type: "line",
      symbolSize: 0,
      data: _data.seriesData
    }
  ]
};

// 生成测试数据
function generateData1() {
  const seriesData: [number, number][] = [];
  const time = new Date("2024-04-09T09:30:00Z");
  const endTime = new Date("2024-04-09T15:00:00Z").getTime();
  const breakStart = new Date("2024-04-09T11:30:00Z").getTime();
  const breakEnd = new Date("2024-04-09T13:00:00Z").getTime();

  let val = 1669;
  while (time.getTime() <= endTime) {
    if (time.getTime() <= breakStart || time.getTime() >= breakEnd) {
      val = val + Math.floor((Math.random() - 0.5 * Math.sin(val / 1000)) * 20 * 100) / 100;
      val = +val.toFixed(2);
      seriesData.push([time.getTime(), val]);
    }
    time.setMinutes(time.getMinutes() + 1);
  }
  return { seriesData, breakStart, breakEnd };
}

onMounted(() => {
  const chartDom = document.getElementById("main");
  if (chartDom) {
    myChart = echarts.init(chartDom);
    myChart.setOption(option);

    window.addEventListener("resize", () => {
      myChart.resize();
    });
  }
});
</script>
