<template>
  <div class="flex h-full flex-col">
    <div class="grid flex-1 grid-cols-[40%_auto] gap-x-5 overflow-hidden">
      <div class="flex h-full flex-1 flex-col overflow-hidden">
        <p>台账树</p>
        <ScrollArea class="flex-1 overflow-hidden">
          <n-tree :data="ledgerData" expand-on-click></n-tree>
        </ScrollArea>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { h } from "vue";
import { type TreeOption, type DataTableColumn, type DataTableRowData } from "naive-ui";
import { ScrollArea } from "@/components/ui/scroll-area";

const ledgerData: TreeOption[] = [
  {
    key: "1",
    label: "以和机器人",
    children: [
      {
        key: "1-1",
        label: "区域"
      },
      {
        key: "1-2",
        label: "人工拍摄区域"
      },
      {
        key: "1-3",
        label: "新地图"
      },
      {
        key: "1-4",
        label: "测试"
      }
    ]
  }
];

const columns: DataTableColumn[] = [
  {
    key: "partName",
    title: "部件名称",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: "detection",
    title: "检测值",
    align: "center"
  },
  {
    key: "status",
    title: "状态",
    align: "center",
    render() {
      return h("span", { class: "text-green-800" }, "正常");
    }
  },
  {
    key: "inspectTime",
    title: "巡检时间",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  }
];
</script>
