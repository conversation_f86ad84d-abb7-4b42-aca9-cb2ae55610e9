<!-- 历史视频 -->
<template>
  <div class="flex h-full flex-col">
    <p class="text-center">机器人：N00001</p>
    <!-- 模拟视频播放器区域 -->
    <div class="relative">
      <video ref="videoRef" controls preload="metadata" class="video-dark-overlay h-[420px] w-full bg-[#2b2b2b] md:h-[480px]">
        <!-- 没有实际视频时可保持空 -->
        <source type="video/mp4" />
        您的浏览器不支持 video 标签。
      </video>

      <!-- 可以在视频上方放一个很细的边框线（模拟画面顶部的浅色线） -->
      <div class="pointer-events-none absolute right-0 bottom-0 left-0 h-12" />
    </div>
    <p class="mt-5 text-center">2025年08月21号</p>
    <div class="flex justify-center">
      <ButtonGroup>
        <Button><ChevronLeft /></Button>
        <Button><ChevronRight /></Button>
      </ButtonGroup>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ButtonGroup, But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-vue-next";
</script>
