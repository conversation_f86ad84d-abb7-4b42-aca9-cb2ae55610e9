<!-- 基本信息 -->
<template>
  <div class="flex h-full flex-col">
    <ul class="mb-2 grid grid-cols-4">
      <li class="flex flex-col items-center bg-muted py-2 text-muted-foreground">
        <span class="font-black text-[#fff08b]">--</span>
        <span>设备名称</span>
      </li>
      <li class="flex flex-col items-center bg-muted py-2 text-muted-foreground">
        <span class="font-black text-[#38b6ff]">--</span>
        <span>完成时间</span>
      </li>
      <li class="flex flex-col items-center bg-muted py-2 text-muted-foreground">
        <span class="font-black text-[#ee4b18]">--</span>
        <span>巡检值</span>
      </li>
      <li class="flex flex-col items-center bg-muted py-2 text-muted-foreground">
        <span class="font-black text-[red]">--</span>
        <span>设备名称</span>
      </li>
    </ul>
    <n-data-table
      :data="data"
      :columns="columns"
      flex-height
      style="height: 100%"
      :pagination="{
        showSizePicker: true,
        showQuickJumper: true
      }"
    />
  </div>
</template>

<script setup lang="ts">
import { type DataTableRowData, type DataTableColumn } from "naive-ui";

const data: DataTableRowData[] = [];

const columns: DataTableColumn[] = [
  {
    key: "inspectionTime",
    title: "巡检时间",
    align: "center"
  },
  {
    key: "inspectionValue",
    title: "巡检值",
    align: "center"
  },
  {
    key: "status",
    title: "状态",
    align: "center"
  }
];
</script>
