<!-- 审核机器人状态 -->
<template>
  <n-modal v-model:show="showModal" preset="card" :title="`${title}`" style="width: 600px">
    <n-image-group show-toolbar-tooltip>
      <n-space>
        <n-image width="80" preview-disabled src="https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg" />
      </n-space>
    </n-image-group>
  </n-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";

const showModal = ref(false);
const title = ref("");

const formData = ref({
  name: "",
  parentMenu: "",
  encoding: "",
  state: 1,
  description: ""
});

const open = (param: any) => {
  Object.assign(formData.value, {
    ...param.row,
    state: param.row.state ? 1 : 0
  });

  title.value = param.title;
  showModal.value = true;
};

defineExpose({
  open
});
</script>
