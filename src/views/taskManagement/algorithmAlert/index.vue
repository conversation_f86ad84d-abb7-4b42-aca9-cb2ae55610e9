<!-- 算法告警信息 -->
<template>
  <div class="grid h-full gap-x-5">
    <Card>
      <CardHeader class="border-b">
        <CardTitle>算法告警信息</CardTitle>
      </CardHeader>
      <CardContent class="flex h-full flex-col space-y-2">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <span class="whitespace-nowrap">时间：</span>
            <n-date-picker type="daterange" clearable />
            <span class="whitespace-nowrap">事件类型：</span>
            <n-select class="w-60!" placeholder="请选择状态" :options="stateList"></n-select>
            <Button class="cursor-pointer">搜索</Button>
          </div>
        </div>
        <n-data-table
          :columns="columns"
          :data="data"
          class="h-full"
          default-expand-all
          :row-key="(row: RowData) => row.id"
          :pagination="{
            showQuickJumper: true,
            showSizePicker: true,
            displayOrder: ['pages', 'quick-jumper', 'size-picker'],
            suffix: info => {
              return '共' + info.itemCount + '条';
            },
            pageSizes: [10, 20, 50, 100]
          }"
        />
      </CardContent>
    </Card>

    <!-- 图片弹框 -->
    <ImgDialog ref="imgDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { h, ref } from "vue";
import { NButton, NImage, NImageGroup, NSpace } from "naive-ui";
import type { DataTableColumns } from "naive-ui";
import ImgDialog from "./components/ImgDialog.vue";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface RowData {
  time: string;
  id: number;
  img: string;
  index: string;
  encoding: string;
  state: boolean;
  children?: RowData[];
}
const imgDialogRef = ref<InstanceType<typeof ImgDialog>>();

const imgDialogInfo = (title: string, row?: any) => {
  console.log(title, row);
  const param = {
    title: title,
    row: { ...row },
    parentMenu: row
  };

  imgDialogRef.value?.open(param);
};
const stateList = [
  {
    label: "安全帽",
    value: 1
  },
  {
    label: "反光衣",
    value: 0
  }
];

const data: RowData[] = [
  {
    id: 1,
    time: "2025-01-03 12:00:31",
    index: "01",
    encoding: "admin",
    img: "https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg",
    state: true
  },
  {
    id: 5,
    time: "2025-01-03 12:00:31",
    encoding: "test",
    index: "02",
    img: "https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg",
    state: true
  },
  {
    id: 1,
    time: "2025-01-03 12:00:31",
    index: "01",
    encoding: "admin",
    img: "https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg",
    state: true
  },
  {
    id: 5,
    time: "2025-01-03 12:00:31",
    encoding: "test",
    index: "02",
    img: "https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg",
    state: false
  }
];

const columns: DataTableColumns<RowData> = [
  {
    type: "selection"
  },
  {
    title: "时间",
    key: "time"
  },
  {
    title: "事件类型",
    key: "state",
    render(rowData) {
      return h("span", rowData.state ? "安全帽" : "反光衣");
    }
  },
  {
    title: "图片",
    key: "img",
    render(rowData) {
      return h(
        NImageGroup,
        { showToolbarTooltip: true },
        {
          default: () =>
            h(
              NSpace,
              {},
              {
                default: () => [
                  h(NImage, {
                    style: {
                      width: "40px",
                      height: "40px"
                    },
                    src: rowData.img
                  })
                ]
              }
            )
        }
      );
    }
  },
  {
    title: "路径",
    key: "encoding"
  },
  {
    key: "action",
    title: "操作",
    width: "350",
    align: "center",
    render(row) {
      return [
        h(
          NButton,
          {
            type: "warning",
            ghost: true,
            size: "small",
            style: {
              marginLeft: "10px"
            },
            onClick: () => imgDialogInfo("事件详情", row)
          },
          { default: () => "详情" }
        )
      ];
    }
  }
];
</script>
