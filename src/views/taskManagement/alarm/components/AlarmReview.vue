<template>
  <n-modal v-model:show="showModal" @after-leave="handleAfterLeave">
    <n-card style="width: 600px" title="审核机器人状态" :bordered="false" size="huge" role="dialog" aria-modal="true">
      <n-form
        label-placement="left"
        label-align="right"
        label-width="auto"
        :model="reviewForm"
        :rules="rules"
        ref="reviewFormRef"
      >
        <n-form-item label="机器人">
          <!-- <n-select v-model:value="reviewForm.robotId" :options="globalStore.robotList"></n-select> -->
          <n-input v-model:value="reviewForm.robotName" readonly />
        </n-form-item>
        <n-form-item label="模块">
          <n-input v-model:value="reviewForm.module" readonly />
        </n-form-item>
        <n-form-item label="功能">
          <n-input v-model:value="reviewForm.fun" readonly />
        </n-form-item>
        <n-form-item label="告警类型">
          <n-input v-model:value="reviewForm.level" readonly />
        </n-form-item>
        <n-form-item label="异常描述">
          <n-input v-model:value="reviewForm.cause" readonly />
        </n-form-item>
        <n-form-item label="审批意见" path="reviewInfo">
          <n-input v-model:value="reviewForm.reviewInfo" type="textarea" />
        </n-form-item>
        <n-form-item label=" ">
          <n-space>
            <n-button @click="showModal = false">取消</n-button>
            <n-button type="primary" @click="handleSubmit">确定</n-button>
          </n-space>
        </n-form-item>
      </n-form>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, useTemplateRef } from "vue";
import { useMessage, type FormRules } from "naive-ui";
import _ from "lodash";

import { alarmReviewModuleApi } from "@/api/modules/robot";

import { useGlobalStore } from "@/store/modules/global";

interface AlarmInfo {
  id: number;
  robotId: number;
  module: string;
  fun: string;
  level: string;
  cause: string;
}

interface Props {
  raw: AlarmInfo;
  getTableList: () => void;
}

const message = useMessage();

const props = ref<Props>({
  raw: {
    id: 0,
    robotId: 0,
    fun: "",
    module: "",
    level: "",
    cause: ""
  },
  getTableList: () => {}
});

const showModal = ref(false);

const reviewFormRef = useTemplateRef("reviewFormRef");

const rules: FormRules = {
  reviewInfo: { required: true, message: "请输入审核意见" }
};

const initialReviewForm = { robotName: "", module: "", fun: "", level: "", cause: "", reviewInfo: "" };

const reviewForm = ref(_.clone(initialReviewForm));

const globalStore = useGlobalStore();

const handleAfterLeave = () => {
  reviewForm.value = _.clone(initialReviewForm);
};

const handleSubmit = async () => {
  await reviewFormRef.value?.validate();
  await alarmReviewModuleApi({
    id: props.value.raw.id,
    reviewInfo: reviewForm.value.reviewInfo
  });
  message.success("审核成功");
  showModal.value = false;
  props.value.getTableList();
};

const viewReviewForm = () => {
  const robotName = globalStore.robotList.find(v => v.id === props.value.raw.robotId)?.robotName ?? "";
  const levelName = globalStore.findDictDataItemInfoToLevel(1, props.value.raw.level).label;
  reviewForm.value.robotName = robotName;
  reviewForm.value.level = levelName;
  reviewForm.value.module = props.value.raw.module;
  reviewForm.value.fun = props.value.raw.fun;
  reviewForm.value.cause = props.value.raw.cause;
};

const open = (_props: Props) => {
  props.value = _props;
  viewReviewForm();
  showModal.value = true;
};

defineExpose({
  open
});
</script>
