<template>
  <Card class="h-full overflow-hidden">
    <CardHeader>
      <p>告警信息</p>
      <div class="w-full">
        <n-form inline label-placement="left" :model="searchParam">
          <n-form-item label="机器人">
            <n-select
              style="width: 150px"
              :options="globalStore.robotList"
              v-model:value="searchParam.robotId"
              label-field="robotName"
              value-field="id"
              clearable
            ></n-select>
          </n-form-item>
          <n-form-item label="审核状态">
            <n-select
              style="width: 150px"
              v-model:value="searchParam.reviewState"
              :options="globalStore.getDictDataByTypeId(4)"
              clearable
            />
          </n-form-item>
          <n-form-item label="告警级别" path="level">
            <n-select
              style="width: 150px"
              v-model:value="searchParam.level"
              :options="globalStore.getDictDataByTypeId(1)"
              clearable
            />
          </n-form-item>
          <n-form-item label="日期">
            <n-date-picker type="daterange" v-model:value="searchParam.time" />
          </n-form-item>
          <n-form-item>
            <n-space>
              <n-button type="primary" @click="handleSearch">搜索</n-button>
              <n-button @click="handleReset">重置</n-button>
            </n-space>
          </n-form-item>
        </n-form>
      </div>
    </CardHeader>
    <CardContent class="h-full">
      <n-data-table
        remote
        :data="tableData"
        :columns="columns"
        flex-height
        style="height: 100%"
        striped
        :pagination="{
          showQuickJumper: true,
          showSizePicker: true,
          itemCount: pageable.total,
          displayOrder: ['pages', 'quick-jumper', 'size-picker'],
          suffix: info => {
            return '共' + info.itemCount + '条';
          },
          pageSize: pageable.limit,
          page: pageable.page,
          pageSizes: [10, 20, 50, 100],
          onUpdatePage: handleCurrentChange,
          onUpdatePageSize: handleSizeChange
        }"
      />

      <AlarmReview ref="alarmReview" />
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { onMounted, h, useTemplateRef } from "vue";
import { type DataTableColumn } from "naive-ui";
import { NButton } from "naive-ui";
import dayjs from "dayjs";

import { robotAlarmListApi } from "@/api/modules/robot";
import type { RobotAlarmResponse } from "@/api/interface/robot";

import { Card, CardHeader, CardContent } from "@/components/ui/card";

import { useGlobalStore } from "@/store/modules/global";

import { useTable } from "@/hooks";

import AlarmReview from "./components/AlarmReview.vue";

const globalStore = useGlobalStore();

const alarmReviewRef = useTemplateRef("alarmReview");

const { tableData, searchParam, getTableList, pageable, handleCurrentChange, handleSizeChange, search, reset } =
  useTable(robotAlarmListApi);

const columns: DataTableColumn<RobotAlarmResponse>[] = [
  {
    key: "robotId",
    title: "机器人",
    align: "center",
    ellipsis: { tooltip: true },
    render: rawData => globalStore.findRobotInfo(rawData.robotId)
  },
  {
    key: "module",
    title: "模块",
    align: "center",
    ellipsis: { tooltip: true }
  },
  {
    key: "fun",
    title: "功能",
    align: "center",
    ellipsis: { tooltip: true }
  },
  {
    key: "level",
    title: "告警级别",
    align: "center",
    ellipsis: { tooltip: true },
    render: rawData =>
      h(
        "span",
        { class: rawData.level === "NORMAL" ? "text-[green]" : "text-[red]" },
        { default: () => globalStore.findDictDataItemInfoToLevel(1, rawData.level).label }
      )
  },
  {
    key: "cause",
    title: "描述",
    align: "center",
    ellipsis: { tooltip: true }
  },
  {
    key: "createTime",
    title: "创建时间",
    align: "center",
    ellipsis: { tooltip: true },
    width: 200,
    render: rawData => dayjs(rawData.createTime).format("YYYY-MM-DD HH:mm:ss")
  },
  {
    key: "userName",
    title: "审核人",
    align: "center",
    ellipsis: { tooltip: true }
  },
  {
    key: "reviewTime",
    title: "审核时间",
    align: "center",
    ellipsis: { tooltip: true },
    render: rawData => (rawData.reviewTime ? dayjs(rawData.reviewTime).format("YYYY-MM-DD HH:mm:ss") : "")
  },
  {
    key: "alarmInfo",
    title: "审核意见",
    align: "center",
    ellipsis: { tooltip: true }
  },
  {
    key: "operation",
    title: "操作",
    align: "center",
    render: rawData =>
      h(
        NButton,
        { type: "primary", disabled: rawData.reviewState === 1, onClick: () => handleOpenAlarmReview(rawData) },
        { default: () => (rawData.reviewState === 1 ? "已审核" : "审核") }
      )
  }
];

const handleSearch = () => {
  // console.log(searchParam.value.time);
  const newSearchParam = { ...searchParam.value };
  if (newSearchParam.time) {
    newSearchParam.startTime = dayjs(newSearchParam.time[0]).format("YYYY-MM-DD 00:00:00");
    newSearchParam.endTime = dayjs(newSearchParam.time[1]).format("YYYY-MM-DD 23:59:59");
  }
  delete newSearchParam.time;
  searchParam.value = { ...newSearchParam };
  search();
};

const handleReset = () => {
  searchParam.value.time = null;
  reset();
};

const handleOpenAlarmReview = (item: RobotAlarmResponse) => {
  alarmReviewRef.value?.open({
    getTableList,
    raw: { id: item.id, robotId: item.robotId, module: item.module, fun: item.fun, level: item.level, cause: item.cause }
  });
};

onMounted(() => {
  searchParam.value.robotId = 1;
  getTableList();
});
</script>

<style lang="scss"></style>
