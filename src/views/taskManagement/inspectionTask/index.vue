<template>
  <div class="flex h-full">
    <!-- 巡检任务信息 -->
    <Card>
      <CardHeader class="flex justify-between border-b">
        <p>巡检任务信息</p>
      </CardHeader>
      <CardContent class="flex h-full flex-col space-y-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-1">
            <span class="whitespace-nowrap">名称：</span>
            <n-input v-model:value="searchParam.taskName" clearable placeholder="请输入任务名称" />
            <n-button @click="search" type="info" class="mr-1!">搜索</n-button>
            <n-button @click="handleNewTaskInfo" type="primary">新增 <PlusIcon class="ml-2 size-4" /></n-button>
          </div>
        </div>
        <n-data-table
          remote
          :data="tableData"
          :columns="columns"
          flex-height
          style="height: 100%"
          :row-key="(row: PlanListResult) => row.id"
          striped
          :pagination="{
            showQuickJumper: true,
            showSizePicker: true,
            itemCount: pageable.total,
            displayOrder: ['pages', 'quick-jumper', 'size-picker'],
            suffix: info => {
              return '共' + info.itemCount + '条';
            },
            pageSize: pageable.limit,
            page: pageable.page,
            pageSizes: [10, 20, 50, 100],
            onUpdatePage: handleCurrentChange,
            onUpdatePageSize: handleSizeChange
          }"
        ></n-data-table>
      </CardContent>
    </Card>

    <new-task-info ref="newTaskInfoRef" />
    <edit-task ref="editTaskRef" />
    <ScheduledExecution ref="scheduledExecution" />
    <SelectionPatrolNodeList view ref="selectionPatrolNodeList" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, h, useTemplateRef } from "vue";
import { PlusIcon, PenLineIcon, Trash2Icon, Clock3Icon, CirclePlayIcon } from "lucide-vue-next";
import { type DataTableColumn } from "naive-ui";
import { useDialog, useMessage, NButton } from "naive-ui";
import dayjs from "dayjs";

import { Card, CardHeader, CardContent } from "@/components/ui/card";

import { useTable } from "@/hooks";

import type { PlanListResult } from "@/api/interface/plan";
import { planListApi, planDeleteApi, sendPatrolTaskMessageApi } from "@/api/modules/plan";

import { INTELLIGENCE_VALIDITY_MAP, INSPECTION_TYPE_ID_MAP } from "@/dictionary";

import { useGlobalStore } from "@/store/modules/global";

import NewTaskInfo from "./components/NewTaskInfo.vue";
import EditTask from "./components/EditTask.vue";
import ScheduledExecution from "./components/ScheduledExecution.vue";
import SelectionPatrolNodeList from "./components/SelectionPatrolNodeList.vue";

const { getTableList, searchParam, tableData, pageable, handleCurrentChange, handleSizeChange, search } = useTable(planListApi);

const dialog = useDialog();
const message = useMessage();

const globalStore = useGlobalStore();

const newTaskInfoRef = useTemplateRef("newTaskInfoRef");
const editTaskRef = useTemplateRef("editTaskRef");
const scheduledExecutionRef = useTemplateRef("scheduledExecution");
const selectionPatrolNodeListRef = useTemplateRef("selectionPatrolNodeList");

const columns: DataTableColumn<PlanListResult>[] = [
  {
    type: "selection"
  },
  {
    key: "planName",
    title: "任务名称",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: "robotName",
    title: "执行机器人",
    align: "center",
    render: rawData => globalStore.findRobotInfo(rawData.robotId)
  },
  {
    key: "insertTime",
    title: "创建时间",
    align: "center",
    width: 200,
    render: rawData => {
      return dayjs(rawData.insertTime).format("YYYY-MM-DD HH:mm:ss");
    }
  },
  {
    key: "typeId",
    title: "任务类型",
    align: "center",
    render: rowData => {
      return INSPECTION_TYPE_ID_MAP[rowData.typeId];
    }
  },
  {
    key: "validity",
    title: "是否有效",
    align: "center",
    render: rowData => {
      return INTELLIGENCE_VALIDITY_MAP[rowData.state];
    }
  },
  {
    key: "priority",
    title: "任务优先级",
    align: "center"
  },
  {
    key: "carSpeed",
    title: "车载速度",
    align: "center"
  },
  {
    key: "patrolList",
    title: "巡检点",
    align: "center",
    render: rawData =>
      h(NButton, { text: true, onClick: () => handleViewPatrolList(rawData.patrolNodeList) }, { default: () => "查看" })
  },
  {
    width: 300,
    key: "execute",
    title: "执行",
    align: "center",
    render: rawData => {
      return [
        h(
          NButton,
          { onClick: () => handleImmediateExecution(rawData), type: "primary", class: "mr-1!" },
          { default: () => ["立即执行", h(CirclePlayIcon, { class: "ml-1 size-4" })] }
        ),
        h(
          NButton,
          { class: "ml-1", onClick: () => handleScheduledExecution(), type: "primary" },
          { default: () => ["定时执行", h(Clock3Icon, { class: "ml-1 size-4" })] }
        )
      ];
    }
  },
  {
    width: 200,
    key: "operation",
    title: "操作",
    align: "center",
    render: rawData => {
      return [
        h(
          NButton,
          { onClick: () => handleEditTask(rawData), type: "primary", class: "mr-1!" },
          { default: () => ["修改", h(PenLineIcon, { class: "ml-1 size-4" })] }
        ),
        h(
          NButton,
          { type: "error", onClick: () => handleDelete(rawData.id) },
          { default: () => ["删除", h(Trash2Icon, { class: "ml-1 size-4" })] }
        )
      ];
    }
  }
];

// 新增
const handleNewTaskInfo = () => {
  newTaskInfoRef.value?.open({
    getTableList: getTableList,
    robotList: globalStore.robotList
  });
};

// 修改
const handleEditTask = (item: PlanListResult) => {
  editTaskRef.value?.open({
    getTableList: getTableList,
    raw: { ...item, patrolNodeIdList: item.patrolNodeList.map(v => v.id) },
    robotList: globalStore.robotList
  });
};

// 删除
const handleDelete = (id: number) => {
  dialog.warning({
    title: "删除",
    content: "确定要删除吗？",
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      await planDeleteApi({ id });
      message.success("删除成功");
      getTableList();
    }
  });
};

// 立即执行
const handleImmediateExecution = (item: PlanListResult) => {
  dialog.info({
    title: "立即执行",
    content: "是否立即执行",
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      await sendPatrolTaskMessageApi({ rbPlanId: item.id, ctrlType: "2002" });
      message.success("执行成功");
      getTableList();
    }
  });
};

// 定时执行
const handleScheduledExecution = () => {
  scheduledExecutionRef.value?.open();
};

const handleViewPatrolList = (list: PlanListResult["patrolNodeList"]) => {
  selectionPatrolNodeListRef.value?.open(list);
};

onMounted(() => {
  getTableList();
});
</script>
