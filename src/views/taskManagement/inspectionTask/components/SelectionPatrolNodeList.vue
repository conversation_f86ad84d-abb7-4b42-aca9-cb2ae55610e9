<template>
  <div>
    <n-button v-if="!view" type="info" @click="handleSelection">选择巡检点</n-button>
    <n-modal v-model:show="showModal" preset="card" style="width: 600px" @after-leave="handleAfterLeave">
      <n-data-table
        v-if="!view"
        :data="list"
        :columns="columns"
        style="height: 300px"
        flex-height
        :row-key="rawData => rawData.id"
        :loading="loading"
        v-model:checked-row-keys="model"
      ></n-data-table>
      <n-data-table
        v-else
        :data="showList"
        :columns="viewColumns"
        style="height: 300px"
        flex-height
        :row-key="rawData => rawData.id"
      ></n-data-table>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type { DataTableColumn, DataTableRowKey } from "naive-ui";

import { patrolNodeListApi } from "@/api/modules/plan";
import type { PatrolNodeListResult } from "@/api/interface/plan";

import { useList } from "@/hooks";

interface Props {
  view?: boolean;
}

const props = withDefaults(defineProps<Props>(), { view: false });

const [list, getList, loading] = useList(patrolNodeListApi);

const showModal = ref(false);

const model = defineModel<DataTableRowKey[]>("value", { default: () => [] });
const showList = ref<PatrolNodeListResult[]>([]);

const columns: DataTableColumn[] = [
  {
    type: "selection"
  },
  {
    key: "name",
    title: "名称",
    align: "center"
  },
  {
    key: "x",
    title: "X坐标",
    align: "center"
  },
  {
    key: "y",
    title: "Y坐标",
    align: "center"
  },
  {
    key: "z",
    title: "Z坐标",
    align: "center"
  }
];

const viewColumns: DataTableColumn[] = [
  {
    key: "name",
    title: "名称",
    align: "center"
  },
  {
    key: "x",
    title: "X坐标",
    align: "center"
  },
  {
    key: "y",
    title: "Y坐标",
    align: "center"
  },
  {
    key: "z",
    title: "Z坐标",
    align: "center"
  }
];

const handleSelection = () => {
  showModal.value = true;
  getList();
};

const handleAfterLeave = () => {
  list.value = [];
  showList.value = [];
};

const open = (list: PatrolNodeListResult[]) => {
  showModal.value = true;
  showList.value = list;
};

defineExpose({
  open
});
</script>
