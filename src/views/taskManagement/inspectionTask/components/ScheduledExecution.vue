<template>
  <n-modal v-model:show="showModal" preset="card" style="width: 1000px">
    <template #header>巡检任务信息</template>
    <n-space vertical>
      <n-button type="primary" @click="newScheduledTaskRef?.open()">新增</n-button>
      <n-data-table
        style="height: 600px"
        remote
        :data="[{}]"
        :columns="columns"
        :row-key="(row: any) => row.id"
        striped
        :pagination="{
          showQuickJumper: true,
          showSizePicker: true,
          itemCount: pageable.total,
          displayOrder: ['pages', 'quick-jumper', 'size-picker'],
          suffix: info => {
            return '共' + info.itemCount + '条';
          },
          pageSize: pageable.limit,
          page: pageable.page,
          pageSizes: [10, 20, 50, 100],
          onUpdatePage: handleCurrentChange,
          onUpdatePageSize: handleSizeChange
        }"
      />
    </n-space>
    <NewScheduledTask ref="newScheduledTask" />
  </n-modal>
</template>

<script setup lang="ts">
import { ref, h, useTemplateRef } from "vue";
import type { DataTableColumn } from "naive-ui";
import { NButton, NSpace } from "naive-ui";

import { useTable } from "@/hooks";

import NewScheduledTask from "./NewScheduledTask.vue";

const showModal = ref(false);

const newScheduledTaskRef = useTemplateRef("newScheduledTask");
const { getTableList, tableData, pageable, handleCurrentChange, handleSizeChange } = useTable();

// columns
const columns: DataTableColumn[] = [
  {
    key: "",
    title: "计划",
    align: "center"
  },
  {
    key: "",
    title: "规则名称",
    align: "center"
  },
  {
    key: "",
    title: "执行时间",
    align: "center"
  },
  {
    key: "",
    title: "重复类型",
    align: "center"
  },
  {
    key: "",
    title: "星期",
    align: "center"
  },
  {
    key: "",
    title: "状态",
    align: "center"
  },
  {
    key: "",
    title: "操作",
    align: "center",
    render: () =>
      h(
        NSpace,
        { justify: "center" },
        {
          default: () => [
            h(NButton, { type: "info" }, { default: () => "修改" }),
            h(NButton, { type: "error" }, { default: () => "删除" })
          ]
        }
      )
    // render: () => [h(NButton, {}, { default: () => "修改" }), h(NButton, {}, { default: () => "删除" })]
  }
];

const open = () => {
  showModal.value = true;
};

defineExpose({
  open
});
</script>
