<template>
  <n-modal v-model:show="showModal" preset="card" style="width: 600px" @after-leave="handleAfterLeave">
    <template #header>新增定时任务</template>
    <n-form label-align="right" label-placement="left" label-width="auto" :rules="rules">
      <!-- 关联的计划 -->
      <n-form-item label="关联的计划">
        <n-input />
      </n-form-item>
      <!-- 月份 -->
      <n-form-item label="月份">
        <n-input />
      </n-form-item>
      <!-- 周数 -->
      <n-form-item label="周数">
        <n-input />
      </n-form-item>
      <!-- 星期 -->
      <n-form-item label="星期">
        <n-input />
      </n-form-item>
      <!-- 间隔类型 -->
      <n-form-item label="间隔类型">
        <n-input />
      </n-form-item>
      <!-- 间隔次数 -->
      <n-form-item label="间隔次数">
        <n-input />
      </n-form-item>
      <!-- 首次执行时间 -->
      <n-form-item label="首次执行时间">
        <n-input />
      </n-form-item>
      <!-- 规则名称 -->
      <n-form-item label="规则名称">
        <n-input />
      </n-form-item>
      <!-- 状态 -->
      <n-form-item label="状态">
        <n-radio-group>
          <n-radio>无效</n-radio>
          <n-radio>有效</n-radio>
          <n-radio>已执行</n-radio>
        </n-radio-group>
      </n-form-item>
      <n-form-item label=" ">
        <n-space>
          <n-button type="primary" @click="handleSubmit">提交</n-button>
          <n-button @click="showModal = false">取消</n-button>
        </n-space>
      </n-form-item>
    </n-form>
  </n-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type { FormRules } from "naive-ui";

const showModal = ref(false);

const rules: FormRules = {};

const open = () => {
  showModal.value = true;
};

const handleSubmit = () => {};

const handleAfterLeave = () => {};

defineExpose({
  open
});
</script>
