<template>
  <n-modal v-model:show="showModal" @after-leave="handleAfterLeave">
    <n-card style="width: 600px" title="修改巡检任务信息" :bordered="false" size="huge" role="dialog" aria-modal="true">
      <n-form label-placement="left" label-width="auto" show-require-mark :model="formData" ref="formRef" :rules="rules">
        <!-- 机器人 -->
        <n-form-item label="机器人" path="robotId">
          <n-select
            placeholder="请选择机器人"
            v-model:value="formData.robotId"
            :options="props.robotList"
            label-field="robotName"
            value-field="id"
          ></n-select>
        </n-form-item>
        <!-- 巡检点 -->
        <n-form-item label="巡检点"></n-form-item>
        <!-- 任务类别 -->
        <n-form-item label="任务类别" path="typeId">
          <n-select
            placeholder="请选择任务类别"
            v-model:value="formData.typeId"
            :options="globalStore.getDictDataByTypeId(2)"
          ></n-select>
        </n-form-item>
        <!-- 任务名称 -->
        <n-form-item label="任务名称" path="planName">
          <n-input v-model:value="formData.planName" clearable placeholder="请输入任务名称" />
        </n-form-item>
        <!-- 是否有效 -->
        <n-form-item label="是否有效" path="state">
          <n-radio-group v-model:value="formData.state">
            <n-radio v-for="item in intelligenceValidity" :key="item.value" :value="item.value">{{ item.label }}</n-radio>
          </n-radio-group>
        </n-form-item>
        <!-- 任务优先级别 -->
        <n-form-item label="任务优先级别" path="priority">
          <!-- <n-input-number v-model:value="formData.priority" :step="1" :min="0" :max="9" placeholder="任务级别(0-9)" /> -->
          <n-select
            v-model:value="formData.priority"
            :options="globalStore.getDictDataByTypeId(3)"
            placeholder="任务优先级"
          ></n-select>
        </n-form-item>
        <!-- 车载速度 -->
        <n-form-item label="车载速度" path="carSpeed">
          <n-input-number
            v-model:value="formData.carSpeed"
            :step="0.1"
            :min="0.1"
            max="1.5"
            placeholder="0.1 - 1.5"
          ></n-input-number>
        </n-form-item>
        <!-- 巡检点 -->
        <n-form-item label="巡检点" path="patrolNodeIdList">
          <SelectionPatrolNodeList v-model:value="formData.patrolNodeIdList" />
        </n-form-item>
        <n-form-item label=" " :show-require-mark="false">
          <n-space>
            <n-button type="primary" @click="handleSubmit">提交</n-button>
            <n-button @click="handleCancel">取消</n-button>
          </n-space>
        </n-form-item>
      </n-form>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, useTemplateRef } from "vue";
import _ from "lodash";
import type { FormRules } from "naive-ui";
import { useMessage } from "naive-ui";
import { intelligenceValidity } from "@/dictionary";
import type { PlanSaveRequest } from "@/api/interface/plan";
import type { RobotListResult } from "@/api/interface/robot";
import { planEditApi } from "@/api/modules/plan";
import { useGlobalStore } from "@/store/modules/global";
import SelectionPatrolNodeList from "./SelectionPatrolNodeList.vue";

interface Props {
  getTableList: () => void;
  robotList: RobotListResult[];
  raw: {
    id?: number;
    robotId?: number;
    typeId?: number;
    state?: string;
    planName?: string;
    priority?: number;
    carSpeed?: number;
    patrolNodeIdList?: number[]
  };
}

const props = ref<Props>({
  getTableList: () => {},
  robotList: [],
  raw: {}
});

const globalStore = useGlobalStore();
const message = useMessage();

const showModal = ref(false);
const formRef = useTemplateRef("formRef");

const initialFormData: PlanSaveRequest = {};

const formData = ref(_.clone(initialFormData));

const rules: FormRules = {
  robotId: { required: true, message: "请选择机器人" },
  typeId: { required: true, message: "请选择任务类型" },
  planName: { required: true, message: "请输入任务名称" },
  state: { required: true, message: "请选择" },
  priority: { required: true, message: "请输入任务优先级" },
  carSpeed: { required: true, message: "请输入车载速度" },
  patrolNodeIdList: { required: true, message: "请选择巡检点" }
};

const handleCancel = () => {
  showModal.value = false;
};

const handleSubmit = async () => {
  await formRef.value?.validate();
  await planEditApi(formData.value);
  message.success("修改成功");
  showModal.value = false;
  props.value.getTableList();
};

const handleAfterLeave = () => {
  formData.value = _.clone(initialFormData);
};

defineExpose({
  open(_props: Props) {
    props.value = _props;
    formData.value = {
      id: _props.raw.id,
      robotId: _props.raw.robotId,
      typeId: _props.raw.typeId,
      state: _props.raw.state,
      planName: _props.raw.planName,
      priority: _props.raw.priority,
      carSpeed: _props.raw.carSpeed,
      patrolNodeIdList: _props.raw.patrolNodeIdList
    };
    showModal.value = true;
  }
});
</script>
