<template>
  <div
    class="flex min-h-screen w-screen items-center justify-center bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-4"
  >
    <Toaster rich-colors />
    <!-- 登录卡片 -->
    <Card class="w-full max-w-md border-slate-700/50 bg-slate-800/60 shadow-2xl backdrop-blur-sm">
      <div class="p-8">
        <!-- 头部 -->
        <div class="mb-8 text-center">
          <div class="mb-4 flex items-center justify-center gap-3">
            <div class="h-3 w-3 animate-pulse rounded-full bg-blue-500"></div>
            <Bot class="h-8 w-8 text-blue-400" />
            <div class="h-3 w-3 animate-pulse rounded-full bg-blue-500"></div>
          </div>
          <h1 class="mb-2 text-2xl font-bold text-slate-200">{{ title }}</h1>
          <p class="text-sm text-slate-400">请输入账号密码进入巡检系统</p>
        </div>

        <!-- 登录表单 -->
        <form @submit.prevent="handleLogin" class="space-y-6">
          <!-- 用户名输入 -->
          <div class="space-y-2">
            <Label for="username" class="text-sm font-medium text-slate-200">用户名</Label>
            <Input
              id="username"
              v-model="formData.username"
              type="text"
              placeholder="请输入用户名"
              autocomplete="username"
              :class="[
                'border-slate-600/50 bg-slate-700/50 text-slate-200 placeholder:text-slate-400',
                'focus:border-blue-500 focus:ring-blue-500/20',
                usernameError ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' : ''
              ]"
              @blur="validateUsername"
            />
            <p v-if="usernameError" class="text-xs text-red-400">{{ usernameError }}</p>
          </div>

          <!-- 密码输入 -->
          <div class="space-y-2">
            <Label for="password" class="text-sm font-medium text-slate-200">密码</Label>
            <div class="relative">
              <Input
                id="password"
                v-model="formData.password"
                :type="showPassword ? 'text' : 'password'"
                placeholder="请输入密码"
                autocomplete="current-password"
                :class="[
                  'border-slate-600/50 bg-slate-700/50 pr-10 text-slate-200 placeholder:text-slate-400',
                  'focus:border-blue-500 focus:ring-blue-500/20',
                  passwordError ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' : ''
                ]"
                @blur="validatePassword"
              />
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="absolute top-1/2 right-3 -translate-y-1/2 text-slate-400 transition-colors hover:text-slate-200"
              >
                <Eye v-if="!showPassword" class="h-4 w-4" />
                <EyeOff v-else class="h-4 w-4" />
              </button>
            </div>
            <p v-if="passwordError" class="text-xs text-red-400">{{ passwordError }}</p>
          </div>

          <!-- 登录按钮 -->
          <Button
            type="submit"
            :disabled="loading || !isFormValid"
            class="w-full bg-blue-600 py-2.5 font-medium text-white transition-all duration-200 hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50"
          >
            <Loader2 v-if="loading" class="mr-2 h-4 w-4 animate-spin" />
            {{ loading ? "登录中..." : "登录" }}
          </Button>
        </form>

        <!-- 底部装饰 -->
        <div class="mt-8 border-t border-slate-700/50 pt-6">
          <div class="flex items-center justify-center text-xs text-slate-500">
            <span>智能巡检与管理解决方案</span>
          </div>
        </div>
      </div>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { Toaster, toast } from "vue-sonner";
import "vue-sonner/style.css";
import { z } from "zod";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Bot, Eye, EyeOff, Loader2 } from "lucide-vue-next";
import { initDynamicRouter } from "@/router/modules/dynamicRouter";
import { RoutePath } from "@/constants";
import { useAuthStore } from "@/store/modules/auth";
import md5 from "md5";
import { loginApi } from "@/api/modules/user";

const title = import.meta.env.VITE_GLOBAL_APP_TITLE;

const router = useRouter();
const authStore = useAuthStore();

// Zod 验证模式
const loginSchema = z.object({
  username: z.string().min(1, "请输入用户名"),
  password: z.string().min(1, "请输入密码")
});

// 状态定义
const loading = ref(false);
const showPassword = ref(false);
const usernameError = ref("");
const passwordError = ref("");

// 表单数据
const formData = reactive({
  username: "",
  password: ""
});

// 表单验证
const isFormValid = computed(() => {
  return formData.username.trim() !== "" && formData.password.trim() !== "" && !usernameError.value && !passwordError.value;
});

// 验证用户名
const validateUsername = () => {
  try {
    loginSchema.shape.username.parse(formData.username);
    usernameError.value = "";
  } catch (error) {
    if (error instanceof z.ZodError) {
      usernameError.value = error.errors[0]?.message || "用户名格式错误";
    }
  }
};

// 验证密码
const validatePassword = () => {
  try {
    loginSchema.shape.password.parse(formData.password);
    passwordError.value = "";
  } catch (error) {
    if (error instanceof z.ZodError) {
      passwordError.value = error.errors[0]?.message || "密码格式错误";
    }
  }
};

// 登录处理函数
const handleLogin = async () => {
  try {
    // 使用 Zod 验证整个表单
    const validationResult = loginSchema.safeParse(formData);

    if (!validationResult.success) {
      // 显示验证错误
      const errors = validationResult.error.errors;
      errors.forEach(error => {
        if (error.path[0] === "username") {
          usernameError.value = error.message;
        } else if (error.path[0] === "password") {
          passwordError.value = error.message;
        }
      });

      toast.error("请检查输入信息", {
        description: "表单验证失败，请修正错误后重试"
      });
      return;
    }

    // 清除错误信息
    usernameError.value = "";
    passwordError.value = "";
    loading.value = true;

    const res = await loginApi({
      ...formData,
      password: md5(formData.password)
    });
    // 显示成功消息
    toast.success("登录成功！", {
      description: "正在跳转到管理平台..."
    });
    // 设置token
    authStore.setToken(res.data.accessToken);
    authStore.setRole(res.data.role);

    // 初始化动态路由
    await initDynamicRouter();

    // 跳转到首页
    router.push(RoutePath.HOME);
  } catch (error) {
    console.error("登录失败:", error);

    // 显示错误消息
    toast.error("登录失败", {
      description: "请检查用户名和密码是否正确"
    });
  } finally {
    loading.value = false;
  }
};

// 导出login函数供外部使用（如果需要）
defineExpose({
  login: handleLogin
});
</script>
