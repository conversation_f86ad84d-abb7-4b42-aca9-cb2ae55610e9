<!-- 参数信息弹框 -->
<template>
  <n-modal v-model:show="showModal" preset="card" :title="`${title}`" style="width: 600px">
    <n-form label-placement="left" label-width="auto" show-require-mark>
      <!-- 上级 -->
      <n-form-item label="上级">
        <n-input v-model:value="formData.parent" placeholder="请输入上级" />
      </n-form-item>
      <!-- 名称 -->
      <n-form-item label="名称">
        <n-input v-model:value="formData.name" placeholder="请输入名称" />
      </n-form-item>
      <!-- 编码 -->
      <n-form-item label="编码">
        <n-input v-model:value="formData.code" placeholder="请输入编码" />
      </n-form-item>
      <!-- 值 -->
      <n-form-item label="值">
        <n-input v-model:value="formData.value" placeholder="请输入值" />
      </n-form-item>
      <!-- 集控平台值 -->
      <n-form-item label="集控平台值">
        <n-input v-model:value="formData.platformValue" placeholder="请输入集控平台值" />
      </n-form-item>
      <!-- 序号 -->
      <n-form-item label="序号">
        <div class="flex items-center">
          <n-button class="mr-2">-</n-button>
          <n-input-number v-model:value="formData.order" class="w-20" />
          <n-button class="ml-2">+</n-button>
        </div>
      </n-form-item>
      <!-- 描述 -->
      <n-form-item label="描述">
        <n-input v-model:value="formData.description" placeholder="请输入描述" type="textarea" />
      </n-form-item>
      <!-- 状态 -->
      <n-form-item label="状态">
        <n-radio-group v-model:value="formData.state">
          <n-radio v-for="item in intelligenceValidity" :key="item.value" :value="item.value">
            {{ item.label }}
          </n-radio>
        </n-radio-group>
      </n-form-item>
      <n-form-item label=" " :show-require-mark="false">
        <Button class="mr-5">提交</Button>
        <Button @click="handleBack">返回</Button>
      </n-form-item>
    </n-form>
  </n-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { Button } from "@/components/ui/button";
import { intelligenceValidity } from "@/dictionary";

const showModal = ref(false);
const title = ref("");
const handleBack = () => {
  showModal.value = false;
};

const formData = ref({
  parent: "",
  name: "",
  code: "",
  value: "",
  platformValue: "",
  order: 0,
  description: "",
  state: 1
});

const open = (param: any) => {
  Object.assign(formData.value, {
    ...param.row,
    state: param.row.state ? 1 : 0
  });
  title.value = param.title;
  showModal.value = true;
};

defineExpose({
  open
});
</script>
