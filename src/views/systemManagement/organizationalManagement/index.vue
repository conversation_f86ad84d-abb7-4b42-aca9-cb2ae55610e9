<!-- 组织管理 -->
<template>
  <div class="grid h-full gap-x-5">
    <!-- 组织管理 -->
    <Card>
      <CardHeader class="border-b">
        <CardTitle>组织管理</CardTitle>
      </CardHeader>
      <CardContent class="flex h-full flex-col space-y-2">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <span class="whitespace-nowrap">名称：</span>
            <Input class="w-60!" />
            <span class="whitespace-nowrap">状态：</span>
            <n-select class="w-60!" placeholder="请选择状态" :options="stateList"></n-select>
            <Button class="cursor-pointer">搜索</Button>
          </div>
          <div class="space-x-2">
            <ButtonGroup>
              <Button @click="handleMenuInfo('新增')">新增 <PlusIcon class="ml-2 size-4" /></Button>
              <Button>修改 <PenLineIcon class="ml-2 size-4" /></Button>
              <Button variant="destructive">删除 <Trash2Icon class="ml-2 size-4" /></Button>
            </ButtonGroup>
          </div>
        </div>
        <n-data-table
          :columns="columns"
          :data="data"
          class="h-full"
          default-expand-all
          :row-key="(row: RowData) => row.id"
          :pagination="{
            showQuickJumper: true,
            showSizePicker: true,
            displayOrder: ['pages', 'quick-jumper', 'size-picker'],
            suffix: info => {
              return '共' + info.itemCount + '条';
            },
            pageSizes: [10, 20, 50, 100]
          }"
        />
      </CardContent>
    </Card>

    <!-- 组织弹框 -->
    <OrganizationalDialog ref="organizationalDialogRef" />
    <!-- 菜单功能列表 -->
    <MenuFunctionListDialog ref="menuFunctionListDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { h, ref } from "vue";
import { NButton } from "naive-ui";
import type { DataTableColumns } from "naive-ui";
import { PlusIcon, PenLineIcon, Trash2Icon } from "lucide-vue-next";
import { Input } from "@/components/ui/input";
import OrganizationalDialog from "./components/OrganizationalDialog.vue";
// import MenuFunctionListDialog from "./components/MenuFunctionListDialog.vue";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button, ButtonGroup } from "@/components/ui/button";

interface RowData {
  name: string;
  id: number;
  index: string;
  encoding: string;
  isSee: boolean;
  level?: number;
  state: boolean;
  children?: RowData[];
}
const organizationalDialogRef = ref<InstanceType<typeof OrganizationalDialog>>();
// const menuFunctionListDialogRef = ref<InstanceType<typeof MenuFunctionListDialog>>();

const handleMenuInfo = (title: string, row?: any) => {
  console.log(title, row);
  const param = {
    title: title,
    row: { ...row },
    parentMenu: row
  };

  organizationalDialogRef.value?.open(param);
};
const stateList = [
  {
    label: "正常",
    value: 1
  },
  {
    label: "停用",
    value: 0
  }
];

const data: RowData[] = [
  {
    id: 1,
    name: "丰华制造集团",
    index: "01",
    encoding: "dfgen",
    isSee: true,
    state: true,
    level: 0,
    children: [
      {
        id: 2,
        name: "智能产线1",
        index: "01",
        level: 1,
        encoding: "dfthn",
        isSee: true,
        state: true
      },
      {
        id: 2,
        name: "智能产线2",
        encoding: "ggfgen",
        index: "02",
        level: 1,
        isSee: true,
        state: false
      }
    ]
  },
  {
    id: 4,
    name: "莱达威装备制造集团",
    encoding: "dfgen",
    index: "02",
    isSee: true,
    state: true,
    level: 0,
    children: [
      {
        id: 6,
        name: "设备产线1",
        index: "01",
        encoding: "dfthn",
        level: 1,
        isSee: true,
        state: true
      }
    ]
  },
  {
    id: 8,
    name: "金世纪制造集团",
    encoding: "dfgen",
    index: "02",
    isSee: true,
    state: true,
    level: 0,
    children: []
  }
];

const columns: DataTableColumns<RowData> = [
  {
    type: "selection"
  },
  {
    title: "名称",
    key: "name"
  },
  {
    title: "编码",
    key: "encoding"
  },
  {
    title: "序号",
    key: "index"
  },
  {
    title: "是否可见",
    key: "isSee",
    render(rowData) {
      return h("span", { class: rowData.isSee ? "text-green-800" : "text-red-800" }, rowData.isSee ? "是" : "否");
    }
  },
  {
    title: "状态",
    key: "state",
    render(rowData) {
      return h("span", { class: rowData.state ? "text-green-800" : "text-red-800" }, rowData.state ? "正常" : "停止");
    }
  },
  {
    key: "action",
    title: "操作",
    width: "350",
    align: "center",
    render(row) {
      return [
        h(
          NButton,
          {
            type: "warning",
            ghost: true,
            size: "small",
            style: {
              marginLeft: "10px"
            },
            onClick: () => handleMenuInfo("编辑", row)
          },
          { default: () => "编辑" }
        ),
        row.level === 0 &&
          h(
            NButton,
            {
              type: "warning",
              ghost: true,
              size: "small",
              style: {
                marginLeft: "10px"
              },
              onClick: () => handleMenuInfo("添加子机构", row.name)
            },
            { default: () => "添加子机构" }
          ),
        ((row.level === 0 && !row.children?.length) || row.level === 1) &&
          h(
            NButton,
            {
              type: "error",
              ghost: true,
              size: "small",
              style: {
                marginLeft: "10px"
              }
            },
            { default: () => "删除" }
          )
      ];
    }
  }
];
</script>
