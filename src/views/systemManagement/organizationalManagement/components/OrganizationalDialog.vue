<!-- 组织信息弹框 -->
<template>
  <n-modal v-model:show="showModal" preset="card" :title="`${title}`" style="width: 600px">
    <n-form label-placement="left" label-width="auto" show-require-mark>
      <!-- 上级组织 -->
      <n-form-item label="上级组织">
        <n-input v-model:value="formData.parentMenu" placeholder="请输入上级组织" />
      </n-form-item>
      <!-- 组织名称 -->
      <n-form-item label="组织名称">
        <n-input v-model:value="formData.name" placeholder="请输入组织名称" />
      </n-form-item>
      <!-- 组织类型 -->
      <n-form-item label="组织类型">
        <n-input v-model:value="formData.encoding" placeholder="请输入组织类型" />
      </n-form-item>
      <!-- 负责人名称 -->
      <n-form-item label="负责人名称">
        <n-input placeholder="请输入负责人名称" v-model:value="formData.principal" />
      </n-form-item>
      <!-- 邮箱 -->
      <n-form-item label="邮箱">
        <n-input placeholder="请输入邮箱" v-model:value="formData.mailbox" />
      </n-form-item>
      <!-- 手机号 -->
      <n-form-item label="手机号">
        <n-input placeholder="请输入手机号" v-model:value="formData.phone" />
      </n-form-item>
      <!-- 地址 -->
      <n-form-item label="地址">
        <n-input placeholder="请输入地址" />
      </n-form-item>
      <!-- 状态 -->
      <n-form-item label="状态">
        <n-radio-group v-model:value="formData.state">
          <n-radio v-for="item in intelligenceValidity" :key="item.value" :value="item.value">
            {{ item.label }}
          </n-radio>
        </n-radio-group>
      </n-form-item>
      <!-- 描述 -->
      <n-form-item label="描述">
        <n-input placeholder="请输入菜单描述" type="textarea" />
      </n-form-item>
      <n-form-item label=" " :show-require-mark="false">
        <Button class="mr-5">提交</Button>
        <Button @click="handleBack">返回</Button>
      </n-form-item>
    </n-form>
  </n-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { Button } from "@/components/ui/button";
import { intelligenceValidity } from "@/dictionary";

const showModal = ref(false);
const title = ref("");
const handleBack = () => {
  showModal.value = false;
};

const formData = ref({
  name: "",
  parentMenu: "",
  encoding: "",
  phone: "",
  mailbox: "",
  principal: "",
  address: "",
  state: 1,
  description: ""
});

const open = (param: any) => {
  Object.assign(formData.value, {
    ...param.row,
    state: param.row.state ? 1 : 0
  });
  title.value = param.title;
  showModal.value = true;
};

defineExpose({
  open
});
</script>
