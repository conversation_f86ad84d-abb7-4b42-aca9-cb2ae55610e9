<!-- 角色功能列表 -->
<template>
  <n-modal v-model:show="showModal" preset="card" title="设置功能权限" style="width: 80%">
    <div class="grid grid-cols-[25rem_auto] gap-x-5">
      <!-- 台账选择 -->
      <MenuTree />
      <!-- 选择功能 -->
      <Card>
        <CardHeader class="flex justify-between border-b">
          <p>选择功能</p>
        </CardHeader>
        <CardContent class="flex h-full flex-col space-y-4">
          <n-data-table
            remote
            :data="tableData"
            :columns="columns"
            flex-height
            :style="{ height: '550px' }"
            :row-key="(row: RowData) => row.id"
            striped
            :pagination="{
              showQuickJumper: true,
              showSizePicker: true,
              itemCount: pageable.total,
              displayOrder: ['pages', 'quick-jumper', 'size-picker'],
              suffix: info => {
                return '共' + info.itemCount + '条';
              },
              pageSize: pageable.limit,
              page: pageable.page,
              pageSizes: [10, 20, 50, 100],
              onChange: handleCurrentChange,
              onUpdatePageSize: handleSizeChange
            }"
          ></n-data-table>
        </CardContent>
      </Card>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { type DataTableColumn } from "naive-ui";
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { useTable } from "@/hooks";
import { getIntelligenceTaskListApi } from "@/api/modules/mock";
import { INTELLIGENCE_TYPE_MAP } from "@/dictionary";
import MenuTree from "./components/MenuTree.vue";

interface RowData {
  id: number;
  name: string;
  robot: string;
  createTime: string;
  type: number;
  validity: number;
  level: number;
  speed: number;
}
const showModal = ref(false);

const open = async () => {
  showModal.value = true;
};

onMounted(() => {});
defineExpose({
  open
});

const { getTableList, searchParam, tableData, pageable, handleCurrentChange, handleSizeChange, search } =
  useTable(getIntelligenceTaskListApi);

const columns: DataTableColumn<RowData>[] = [
  {
    type: "selection"
  },
  {
    key: "type",
    title: "功能名称",
    align: "center",
    render: rowData => {
      return INTELLIGENCE_TYPE_MAP[rowData.type];
    }
  }
];

onMounted(() => {
  getTableList();
});
</script>

<style scoped>
:deep(.n-data-table .n-data-table-th) {
  color: #444444 !important;
}
:deep(.n-data-table .n-data-table-td) {
  color: #606060;
}
:deep(.n-pagination .n-pagination-item) {
  color: #606060;
}
</style>
