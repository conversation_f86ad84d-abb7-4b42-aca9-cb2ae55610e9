<!-- 角色弹框 -->
<template>
  <n-modal v-model:show="showModal" preset="card" :title="`${title}`" style="width: 600px">
    <n-form label-placement="left" label-width="auto" show-require-mark>
      <!-- 菜单名称 -->
      <n-form-item label="角色名称">
        <n-input v-model:value="formData.name" placeholder="请输入角色名称" />
      </n-form-item>
      <!-- 菜单代号 -->
      <n-form-item label="角色编码">
        <n-input v-model:value="formData.encoding" placeholder="请输入角色编码" />
      </n-form-item>
      <!-- 状态 -->
      <n-form-item label="状态">
        <n-radio-group v-model:value="formData.state">
          <n-radio v-for="item in intelligenceValidity" :key="item.value" :value="item.value">
            {{ item.label }}
          </n-radio>
        </n-radio-group>
      </n-form-item>
      <!-- 描述 -->
      <n-form-item label="描述">
        <n-input placeholder="请输入菜单描述" type="textarea" />
      </n-form-item>
      <n-form-item label=" " :show-require-mark="false">
        <Button class="mr-5">提交</Button>
        <Button @click="handleBack">返回</Button>
      </n-form-item>
    </n-form>
  </n-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { Button } from "@/components/ui/button";
import { intelligenceValidity } from "@/dictionary";

const showModal = ref(false);
const title = ref("");
const handleBack = () => {
  showModal.value = false;
};

const formData = ref({
  name: "",
  parentMenu: "",
  encoding: "",
  state: 1,
  description: ""
});

const open = (param: any) => {
  Object.assign(formData.value, {
    ...param.row,
    state: param.row.state ? 1 : 0
  });

  title.value = param.title;
  showModal.value = true;
};

defineExpose({
  open
});
</script>
