<!-- 菜单树 -->
<template>
  <Card class="overflow-hidden">
    <CardHeader class="flex items-center justify-between border-b">
      <p>菜单列表</p>
    </CardHeader>
    <CardContent class="flex-1 overflow-hidden">
      <ScrollArea class="h-full">
        <n-tree
          :pattern="keyword"
          :data="data"
          checkable
          :render-prefix="renderPrefix"
          :show-irrelevant-nodes="false"
          default-expand-all
        ></n-tree>
      </ScrollArea>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { ref, h, onMounted } from "vue";
import { SearchIcon, FolderClosedIcon, BadgeAlertIcon } from "lucide-vue-next";
import type { TreeOption } from "naive-ui";

import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";

import { getIntelligenceTreeApi } from "@/api/modules/mock";

const keyword = ref("");

// 台账信息
const data = ref<TreeOption[]>([]);

const renderPrefix = ({ option }: { option: TreeOption }) => {
  return option.children ? h(FolderClosedIcon, { class: "size-4" }) : h(BadgeAlertIcon, { class: "size-4" });
};

const getTreeData = async () => {
  const res = await getIntelligenceTreeApi();
  data.value = res.data as TreeOption[];
};

onMounted(() => {
  getTreeData();
});
</script>
