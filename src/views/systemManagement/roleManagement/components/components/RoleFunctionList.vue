<!-- 角色功能列表 -->
<template>
  <n-modal v-model:show="showModal">
    <n-card style="width: 600px" title="添加巡检任务信息" :bordered="false" size="huge" role="dialog" aria-modal="true">
      <n-form label-placement="left" label-width="auto" show-require-mark>
        <!-- 隧道 -->
        <n-form-item label="隧道">
          <n-select placeholder="请选择隧道"></n-select>
        </n-form-item>
        <!-- 机器人 -->
        <n-form-item label="机器人">
          <n-select placeholder="请选择机器人"></n-select>
        </n-form-item>
        <!-- 任务类别 -->
        <n-form-item label="任务类别">
          <n-select placeholder="请选择任务类别"></n-select>
        </n-form-item>
        <!-- 任务名称 -->
        <n-form-item label="任务名称">
          <n-input />
        </n-form-item>
        <!-- 是否有效 -->
        <n-form-item label="是否有效">
          <n-radio-group>
            <n-radio v-for="item in intelligenceValidity" :key="item.value" :value="item.value">{{ item.label }}</n-radio>
          </n-radio-group>
        </n-form-item>
        <!-- 任务优先级别 -->
        <n-form-item label="任务优先级别">
          <n-select placeholder="请选择任务优先级别"></n-select>
        </n-form-item>
        <!-- 车载速度 -->
        <n-form-item label="车载速度">
          <n-input-number :step="0.1" :min="0.1" max="Infinity"></n-input-number>
        </n-form-item>
        <n-form-item label=" " :show-require-mark="false">
          <Button class="mr-5">提交</Button>
          <Button @click="handleBack">返回</Button>
        </n-form-item>
      </n-form>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { Button } from "@/components/ui/button";
import { intelligenceValidity } from "@/dictionary";

const showModal = ref(false);

const handleBack = () => {
  showModal.value = false;
};

defineExpose({
  open() {
    showModal.value = true;
  }
});
</script>
