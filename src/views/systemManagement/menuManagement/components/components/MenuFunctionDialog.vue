<!-- 菜单信息弹框 -->
<template>
  <n-modal v-model:show="showModal" preset="card" :title="`${title}`" style="width: 600px">
    <n-form label-placement="left" label-width="auto" show-require-mark>
      <!-- 菜单名称 -->
      <n-form-item label="名称">
        <n-input v-model:value="formData.name" placeholder="请输入名称" />
      </n-form-item>
      <!-- 菜单代号 -->
      <n-form-item label="标识">
        <n-input v-model:value="formData.parentMenu" placeholder="标识" />
      </n-form-item>
      <!-- 排序 -->
      <n-form-item label="排序">
        <n-input placeholder="请输入排序" v-model:value="formData.index" />
      </n-form-item>
      <!-- 操作类型 -->
      <n-form-item label="操作类型">
        <n-radio-group v-model:value="formData.operationType" name="radiogroup">
          <n-space>
            <n-radio v-for="song in operationTypeList" :key="song.value" :value="song.value">
              {{ song.label }}
            </n-radio>
          </n-space>
        </n-radio-group>
      </n-form-item>
      <!-- 资源类型 -->
      <n-form-item label="资源类型">
        <n-radio-group v-model:value="formData.resourceType" name="radiogroup">
          <n-space>
            <n-radio v-for="song in resourceTypeList" :key="song.value" :value="song.value">
              {{ song.label }}
            </n-radio>
          </n-space>
        </n-radio-group>
      </n-form-item>
      <!-- 描述 -->
      <n-form-item label="描述">
        <n-input placeholder="请输入描述" type="textarea" />
      </n-form-item>
      <n-form-item label=" " :show-require-mark="false">
        <Button class="mr-5">提交</Button>
        <Button @click="handleBack">返回</Button>
      </n-form-item>
    </n-form>
  </n-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { Button } from "@/components/ui/button";

const showModal = ref(false);
const title = ref("");
const handleBack = () => {
  showModal.value = false;
};

const formData = ref({
  name: "",
  parentMenu: "",
  index: "",
  operationType: "",
  resourceType: ""
});

const operationTypeList = [
  {
    value: "0",
    label: "查询"
  },
  {
    value: "1",
    label: "新增"
  },
  {
    value: "2",
    label: "修改"
  },
  {
    value: "3",
    label: "删除"
  },
  {
    value: "4",
    label: "其他"
  }
];

const resourceTypeList = [
  {
    value: "0",
    label: "按钮"
  },
  {
    value: "1",
    label: "链接"
  },
  {
    value: "2",
    label: "图片"
  },
  {
    value: "3",
    label: "文字"
  }
];

const open = (param: any) => {
  Object.assign(formData.value, {
    ...param.row
  });
  title.value = param.title;
  showModal.value = true;
};

defineExpose({
  open
});
</script>
