<!-- 菜单弹框 -->
<template>
  <n-modal v-model:show="showModal" preset="card" :title="`${title}菜单`" style="width: 600px">
    <n-form label-placement="left" label-width="auto" show-require-mark>
      <!-- 菜单名称 -->
      <n-form-item label="菜单名称">
        <n-input v-model:value="formData.name" placeholder="请输入菜单名称" />
      </n-form-item>
      <!-- 菜单代号 -->
      <n-form-item label="上级菜单">
        <n-input v-model:value="formData.parentMenu" placeholder="请输入上级菜单" />
      </n-form-item>
      <!-- 菜单代号 -->
      <n-form-item label="菜单代号">
        <n-input v-model:value="formData.encoding" placeholder="请输入菜单代号" />
      </n-form-item>
      <!-- 图标 -->
      <n-form-item label="图标">
        <n-input placeholder="请选择图标" />
      </n-form-item>
      <!-- 排序 -->
      <n-form-item label="排序">
        <n-input placeholder="请输入排序" v-model:value="formData.index" />
      </n-form-item>
      <!-- 菜单地址号 -->
      <n-form-item label="菜单地址号">
        <n-input placeholder="请输入菜单地址号" />
      </n-form-item>
      <!-- 是否叶子节点 -->
      <n-form-item label="是否叶子节点">
        <n-radio-group>
          <n-radio v-for="item in intelligenceValidity" :key="item.value" :value="item.value">{{ item.label }}</n-radio>
        </n-radio-group>
      </n-form-item>
      <!-- 状态 -->
      <n-form-item label="状态">
        <n-radio-group v-model:value="formData.state">
          <n-radio v-for="item in intelligenceValidity" :key="item.value" :value="item.value">
            {{ item.label }}
          </n-radio>
        </n-radio-group>
      </n-form-item>
      <!-- 是否可见 -->
      <n-form-item label="是否可见">
        <n-radio-group v-model:value="formData.isSee">
          <n-radio v-for="item in intelligenceValidity" :key="item.value" :value="item.value">{{ item.label }}</n-radio>
        </n-radio-group>
      </n-form-item>
      <!-- 描述 -->
      <n-form-item label="描述">
        <n-input placeholder="请输入菜单描述" type="textarea" />
      </n-form-item>
      <n-form-item label=" " :show-require-mark="false">
        <Button class="mr-5">提交</Button>
        <Button @click="handleBack">返回</Button>
      </n-form-item>
    </n-form>
  </n-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { Button } from "@/components/ui/button";
import { intelligenceValidity } from "@/dictionary";

const showModal = ref(false);
const title = ref("");
const handleBack = () => {
  showModal.value = false;
};

const formData = ref({
  name: "",
  parentMenu: "",
  encoding: "",
  icon: "",
  index: "",
  address: "",
  isLeaf: 1,
  state: 1,
  isSee: 1,
  description: ""
});

const open = (param: any) => {
  Object.assign(formData.value, {
    ...param.row,
    state: param.row.state ? 1 : 0,
    isSee: param.row.isSee ? 1 : 0
  });
  if (param.title == "添加子") {
    formData.value.parentMenu = param.parentMenu;
  }

  title.value = param.title;
  showModal.value = true;
};

defineExpose({
  open
});
</script>
