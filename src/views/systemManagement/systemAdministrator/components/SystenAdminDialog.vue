<!-- 系统管理员弹框 -->
<template>
  <n-modal v-model:show="showModal" preset="card" :title="`${title}`" style="width: 600px">
    <n-form label-placement="left" label-width="auto" show-require-mark>
      <!-- 组织名称 -->
      <n-form-item label="组织">
        <n-input v-model:value="formData.name" placeholder="请输入组织" />
      </n-form-item>
      <!-- 登录名 -->
      <n-form-item label="登录名">
        <n-input v-model:value="formData.loginName" placeholder="请输入登录名" />
      </n-form-item>
      <!-- 用户姓名 -->
      <n-form-item label="用户姓名">
        <n-input v-model:value="formData.name" placeholder="请输入用户姓名" />
      </n-form-item>
      <!-- 密码 -->
      <n-form-item label="密码">
        <n-input v-model:value="formData.password" type="password" placeholder="请输入密码" show-password-on="click" />
      </n-form-item>
      <!-- 确认密码 -->
      <n-form-item label="确认密码">
        <n-input v-model:value="formData.confirmPassword" type="password" placeholder="请输入确认密码" show-password-on="click" />
      </n-form-item>
      <!-- 电话 -->
      <n-form-item label="电话">
        <n-input placeholder="请输入电话" v-model:value="formData.phone" />
      </n-form-item>
      <!-- 邮箱 -->
      <n-form-item label="邮箱">
        <n-input placeholder="请输入邮箱" v-model:value="formData.mailbox" />
      </n-form-item>
      <!-- 角色 -->
      <n-form-item label="角色">
        <n-select v-model:value="formData.role" placeholder="请选择角色" />
      </n-form-item>
      <!-- 用户状态 -->
      <n-form-item label="用户状态">
        <n-radio-group v-model:value="formData.state">
          <n-radio v-for="item in intelligenceValidity" :key="item.value" :value="item.value">
            {{ item.label }}
          </n-radio>
        </n-radio-group>
      </n-form-item>
      <!-- 地址 -->
      <n-form-item label="地址">
        <n-input placeholder="请输入地址" />
      </n-form-item>
      <!-- 描述 -->
      <n-form-item label="备注">
        <n-input placeholder="请输入描述" type="textarea" />
      </n-form-item>
      <n-form-item label=" " :show-require-mark="false">
        <Button class="mr-5">提交</Button>
        <Button @click="handleBack">返回</Button>
      </n-form-item>
    </n-form>
  </n-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { Button } from "@/components/ui/button";
import { intelligenceValidity } from "@/dictionary";

const showModal = ref(false);
const title = ref("");
const handleBack = () => {
  showModal.value = false;
};

const formData = ref({
  loginName: "",
  name: "",
  password: "",
  confirmPassword: "",
  phone: "",
  mailbox: "",
  role: null,
  state: 1
});

const open = (param: any) => {
  Object.assign(formData.value, {
    ...param.row,
    state: param.row.state ? 1 : 0
  });
  title.value = param.title;
  showModal.value = true;
};

defineExpose({
  open
});
</script>
