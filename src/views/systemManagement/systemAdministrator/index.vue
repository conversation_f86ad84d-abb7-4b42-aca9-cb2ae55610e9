<!-- 系统管理员 -->
<template>
  <div class="grid h-full grid-cols-[25rem_auto] gap-x-5">
    <!-- 机构信息 -->
    <SystemAdminTree title="机构信息" />
    <!-- 系统管理员信息 -->
    <Card>
      <CardContent class="flex h-full flex-col space-y-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-1">
            <span class="whitespace-nowrap">上级：</span>
            <Input class="" v-model="searchParam.superiors" />
            <span class="whitespace-nowrap">名称：</span>
            <Input class="" v-model="searchParam.name" />
            <span class="whitespace-nowrap">账号：</span>
            <Input class="" v-model="searchParam.account" />
            <Button class="cursor-pointer" @click="search">搜索</Button>
          </div>
          <div class="space-x-2">
            <ButtonGroup>
              <Button @click="handleNewTaskInfo('新增系统管理员')">新增 <PlusIcon class="ml-2 size-4" /></Button>
              <Button>修改 <PenLineIcon class="ml-2 size-4" /></Button>
              <Button variant="destructive">删除 <Trash2Icon class="ml-2 size-4" /></Button>
            </ButtonGroup>
          </div>
        </div>
        <n-data-table
          remote
          :data="tableData"
          :columns="columns"
          flex-height
          style="height: 100%"
          :row-key="(row: RowData) => row.id"
          striped
          :pagination="{
            showQuickJumper: true,
            showSizePicker: true,
            itemCount: pageable.total,
            displayOrder: ['pages', 'quick-jumper', 'size-picker'],
            suffix: info => {
              return '共' + info.itemCount + '条';
            },
            pageSize: pageable.limit,
            page: pageable.page,
            pageSizes: [10, 20, 50, 100],
            onChange: handleCurrentChange,
            onUpdatePageSize: handleSizeChange
          }"
        ></n-data-table>
      </CardContent>
    </Card>

    <SystenAdminDialog ref="systenAdminDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { PlusIcon, PenLineIcon, Trash2Icon } from "lucide-vue-next";
import { type DataTableColumn, useMessage } from "naive-ui";
import { Card, CardContent } from "@/components/ui/card";
import { Button, ButtonGroup } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useTable } from "@/hooks";
import { getIntelligenceTaskListApi } from "@/api/modules/mock";
import { INTELLIGENCE_VALIDITY_MAP } from "@/dictionary";
import SystemAdminTree from "./components/SystemAdminTree.vue";
import SystenAdminDialog from "./components/SystenAdminDialog.vue";

interface RowData {
  id: number;
  name: string;
  robot: string;
  createTime: string;
  type: number;
  validity: number;
  level: number;
  speed: number;
}

const { getTableList, searchParam, tableData, pageable, handleCurrentChange, handleSizeChange, search } =
  useTable(getIntelligenceTaskListApi);

const systenAdminDialogRef = ref<InstanceType<typeof SystenAdminDialog>>();

const message = useMessage();

const columns: DataTableColumn<RowData>[] = [
  {
    type: "selection"
  },
  {
    key: "name",
    title: "登录账号",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: "robot",
    title: "名称",
    align: "center"
  },
  {
    key: "createTime",
    title: "所属机构",
    align: "center"
  },

  {
    key: "level",
    title: "手机",
    align: "center"
  },
  {
    key: "speed",
    title: "邮箱",
    align: "center"
  },
  {
    key: "speed",
    title: "地址",
    align: "center"
  },
  {
    key: "validity",
    title: "状态",
    align: "center",
    render: rowData => {
      return INTELLIGENCE_VALIDITY_MAP[rowData.validity];
    }
  }
];

const handleNewTaskInfo = (title: string, row?: any) => {
  const params = {
    title: title,
    row: { ...row }
  };
  systenAdminDialogRef.value?.open(params);
};

onMounted(() => {
  getTableList();
});
</script>
