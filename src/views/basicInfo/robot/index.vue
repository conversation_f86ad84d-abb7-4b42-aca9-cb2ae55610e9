<template>
  <div class="flex h-full flex-col gap-y-2">
    <div class="grid flex-1 grid-cols-2 gap-x-2">
      <div class="grid grid-rows-[45%_1fr] gap-y-2">
        <!-- 机器人基本信息编辑 -->
        <Card>
          <CardHeader class="border-b">
            <CardTitle>机器人基本信息编辑</CardTitle>
          </CardHeader>
          <CardContent>
            <n-form show-require-mark label-placement="left" label-width="auto" class="max-w-120 min-w-60">
              <n-form-item label="隧道">
                <n-select placeholder="隧道"></n-select>
              </n-form-item>
              <n-form-item label="机器人编码">
                <n-input></n-input>
              </n-form-item>
              <n-form-item label="机器人名称">
                <n-input></n-input>
              </n-form-item>
              <n-form-item label="通道编码">
                <n-input></n-input>
              </n-form-item>
            </n-form>
          </CardContent>
        </Card>
        <!-- 云台信息 -->
        <Card>
          <CardHeader class="border-b">
            <CardTitle>云台信息</CardTitle>
          </CardHeader>
          <CardContent class="h-full">
            <n-form label-placement="left" label-width="auto" class="max-w-120 min-w-60">
              <template class="block lg:flex">
                <n-form-item label="1倍水平视场角">
                  <n-input value="57.2"></n-input>
                </n-form-item>
                <n-form-item label="1倍垂直视场角">
                  <n-input value="34.69"></n-input>
                </n-form-item>
              </template>
              <n-form-item label="可见光rtmp访问地址">
                <n-input></n-input>
              </n-form-item>
              <n-form-item label="红外光rtmp访问地址">
                <n-input></n-input>
              </n-form-item>
              <n-form-item label="红外光rtsp访问地址">
                <n-input></n-input>
              </n-form-item>
              <n-form-item label="红外光rtsp访问地址">
                <n-input></n-input>
              </n-form-item>
            </n-form>
          </CardContent>
        </Card>
      </div>
      <!-- 集控中心信息 -->
      <Card>
        <CardHeader class="border-b">
          <CardTitle>集控中心信息</CardTitle>
        </CardHeader>
        <CardContent>
          <n-form label-placement="left" label-width="auto" class="max-w-120 min-w-60">
            <n-form-item label="电池额定容量">
              <n-input></n-input>
            </n-form-item>
            <n-form-item label="出厂时间" show-require-mark>
              <n-date-picker class="w-full" clearable type="datetime" placeholder="出厂时间"></n-date-picker>
            </n-form-item>
            <n-form-item label="投运时间" show-require-mark>
              <n-date-picker class="w-full" clearable type="datetime" placeholder="投运时间"></n-date-picker>
            </n-form-item>
            <n-form-item label="生产厂家" show-require-mark>
              <n-select placeholder="生产厂家"></n-select>
            </n-form-item>
            <n-form-item label="机器人类型" show-require-mark>
              <n-select placeholder="请选择机器人类型"></n-select>
            </n-form-item>
            <n-form-item label="状态" show-require-mark>
              <n-radio-group>
                <n-radio :value="1">正常</n-radio>
                <n-radio :value="0">停用</n-radio>
              </n-radio-group>
            </n-form-item>
            <n-form-item label="型号">
              <n-input></n-input>
            </n-form-item>
            <n-form-item label="出厂编号">
              <n-input></n-input>
            </n-form-item>
            <n-form-item label="是否轮转">
              <n-radio-group>
                <n-radio :value="1">是</n-radio>
                <n-radio :value="0">否</n-radio>
              </n-radio-group>
            </n-form-item>
            <n-form-item label="导航类型">
              <n-select placeholder="请选择导航类型"></n-select>
            </n-form-item>
            <n-form-item label="驱动类型">
              <n-select placeholder="请选择驱动类型"></n-select>
            </n-form-item>
          </n-form>
        </CardContent>
      </Card>
    </div>
    <div class="flex justify-center">
      <Button>刷新参数</Button>
      <Button class="ml-10">提交</Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardContent, CardTitle } from "@/components/ui/card";
</script>
