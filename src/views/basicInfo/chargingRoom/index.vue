<template>
  <div class="h-full">
    <Card class="h-full">
      <CardHeader class="border-b">充电房信息编辑</CardHeader>
      <CardContent>
        <n-form show-require-mark label-placement="left" label-width="auto" class="max-w-120 min-w-60">
          <n-form-item label="隧道">
            <n-select placeholder="隧道"></n-select>
          </n-form-item>
          <n-form-item label="机器人">
            <n-select placeholder="请选择机器人"></n-select>
          </n-form-item>
          <n-form-item label="X轴">
            <n-input-number class="w-full"></n-input-number>
          </n-form-item>
          <n-form-item label="Y轴">
            <n-input-number class="w-full"></n-input-number>
          </n-form-item>
          <n-form-item label="状态">
            <n-radio-group>
              <n-radio :value="1">正常</n-radio>
              <n-radio :value="2">停用</n-radio>
            </n-radio-group>
          </n-form-item>
          <n-form-item label=" ">
            <Button>提交</Button>
            <Button variant="outline" class="ml-5">返回</Button>
          </n-form-item>
        </n-form>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
</script>
