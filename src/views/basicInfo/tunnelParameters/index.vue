<template>
  <Card class="h-full">
    <CardContent>
      <Tabs default-value="a">
        <TabsList>
          <TabsTrigger value="a">机构站点信息</TabsTrigger>
          <TabsTrigger value="b">地图参数设置</TabsTrigger>
          <TabsTrigger value="c">地图路线设置</TabsTrigger>
        </TabsList>
        <TabsContent value="a" class="p-4">
          <n-form label-placement="left" label-width="auto" class="w-100">
            <n-form-item label="站点">
              <n-select placeholder="站点"></n-select>
            </n-form-item>
            <n-form-item label="单位">
              <n-input></n-input>
            </n-form-item>
            <n-form-item label="电压级别">
              <n-input></n-input>
            </n-form-item>
            <n-form-item label="站点编码" show-require-mark>
              <n-input></n-input>
            </n-form-item>
            <n-form-item label="巡维中心">
              <n-input></n-input>
            </n-form-item>
            <n-form-item label="地图名称">
              <n-input></n-input>
            </n-form-item>
            <n-form-item label="集控接口">
              <n-input></n-input>
            </n-form-item>
            <n-form-item label=" ">
              <Button>提交</Button>
            </n-form-item>
          </n-form>
        </TabsContent>
      </Tabs>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
</script>
