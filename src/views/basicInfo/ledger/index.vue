<template>
  <div class="grid h-full grid-cols-[24rem_1fr] gap-x-5">
    <!-- 台账树 -->
    <Card>
      <CardHeader class="border-b">
        <div class="mb-1 flex items-center justify-between">
          <CardTitle>台账树</CardTitle>
          <div class="relative flex w-1/2 max-w-sm items-center">
            <Input placeholder="查询" class="py-0 pr-10" v-model="ledgerTreeKeyword" />
            <span class="absolute inset-y-0 end-0 flex items-center justify-center px-2">
              <SearchIcon class="text-muted-foreground" />
            </span>
          </div>
        </div>
        <p>
          <span class="font-bold text-[red] italic">异常信息图例：</span>
          <span class="mr-2">正常</span>
          <span class="mr-2">设备类型</span>
          <span class="mr-2">布点</span>
          <span>识别类型</span>
        </p>
      </CardHeader>
      <CardContent class="flex-1 overflow-hidden">
        <ScrollArea class="h-full">
          <n-tree
            expand-on-click
            block-line
            :pattern="ledgerTreeKeyword"
            :data="ledgerTreeData"
            :show-irrelevant-nodes="false"
            :on-update:expanded-keys="updatePrefixWithExpand"
          ></n-tree>
        </ScrollArea>
      </CardContent>
    </Card>
    <!-- 台帐信息管理 -->
    <Card>
      <CardHeader class="border-b">
        <CardTitle>台帐信息管理</CardTitle>
      </CardHeader>
      <CardContent class="flex h-full flex-col space-y-2">
        <!-- 查询 -->
        <n-form label-placement="left" inline>
          <n-form-item label="名称">
            <n-input></n-input>
          </n-form-item>
          <n-form-item><Button>搜索</Button></n-form-item>
        </n-form>
        <!-- 按钮组 -->
        <div class="space-x-2">
          <ButtonGroup>
            <Button>新增</Button>
            <Button>修改</Button>
            <Button variant="destructive">删除</Button>
          </ButtonGroup>
          <ButtonGroup>
            <Button>模板下载</Button>
            <Button>导入台账</Button>
            <Button>导出台账</Button>
          </ButtonGroup>
          <Button>告警配置</Button>
        </div>
        <n-data-table
          :data="ledgerTableData"
          :columns="ledgerTableColumns"
          flex-height
          :row-key="(row: RowData) => row.id"
          class="flex-1"
        ></n-data-table>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, h } from "vue";
import { SearchIcon, FolderOpenIcon, FolderIcon, BadgeAlert } from "lucide-vue-next";
import type { TreeOption, DataTableColumn, DataTableRowData } from "naive-ui";

import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button, ButtonGroup } from "@/components/ui/button";

interface RowData {
  id: number;
  partName: string;
  identifyType: string;
  deviceType: string;
  alarmType: string;
  unit: string;
}

const ledgerTreeKeyword = ref("");

const ledgerTreeData: TreeOption[] = [
  {
    key: "1",
    label: "以和机器人",
    prefix: () => h(FolderIcon, { class: "size-4" }),
    children: [
      {
        key: "1-1",
        label: "人工拍照区域",
        prefix: () => h(BadgeAlert, { class: "size-4" })
      },
      {
        key: "1-2",
        label: "区域1",
        prefix: () => h(BadgeAlert, { class: "size-4" })
      },
      {
        key: "1-3",
        label: "新地图",
        prefix: () => h(BadgeAlert, { class: "size-4" })
      },
      {
        key: "1-4",
        label: "测试",
        prefix: () => h(BadgeAlert, { class: "size-4" })
      }
    ]
  }
];

const updatePrefixWithExpand = (
  _keys: Array<string | number>,
  _option: Array<TreeOption | null>,
  meta: {
    node: TreeOption | null;
    action: "expand" | "collapse" | "filter";
  }
) => {
  if (!meta.node) return;

  switch (meta.action) {
    case "expand":
      meta.node.prefix = () => h(FolderOpenIcon, { class: "size-4" });
      break;
    case "collapse":
      meta.node.prefix = () => h(FolderIcon, { class: "size-4" });
  }
};

const ledgerTableData: DataTableRowData[] = [
  {
    id: 1,
    partName: "t1设备开关-压接1",
    identifyType: "位置状态识别",
    deviceType: "开关",
    warningType: "外观异常",
    unit: ""
  },
  {
    id: 2,
    partName: "t1设备开关-压接2",
    identifyType: "表计读取",
    deviceType: "开关",
    warningType: "外观异常",
    unit: ""
  },
  {
    id: 3,
    partName: "t1设备开关-压接3",
    identifyType: "开关",
    deviceType: "开关",
    warningType: "外观异常",
    unit: ""
  },
  {
    id: 4,
    partName: "t1设备开关-圆形",
    identifyType: "开关",
    deviceType: "开关",
    warningType: "超温报警",
    unit: "℃"
  },
  {
    id: 5,
    partName: "t1设备开关-方形",
    identifyType: "开关",
    deviceType: "开关",
    warningType: "超温报警",
    unit: "℃"
  },
  {
    id: 6,
    partName: "t1设备指示灯1",
    identifyType: "指示灯",
    deviceType: "开关",
    warningType: "外观异常",
    unit: ""
  },
  {
    id: 7,
    partName: "t1设备指示灯2",
    identifyType: "指示灯",
    deviceType: "开关",
    warningType: "外观异常",
    unit: ""
  },
  {
    id: 8,
    partName: "t1设备指示灯3",
    identifyType: "指示灯",
    deviceType: "开关",
    warningType: "外观异常",
    unit: ""
  },
  {
    id: 9,
    partName: "t1设备电流表1",
    identifyType: "表计读取",
    deviceType: "开关",
    warningType: "仪表超限报警",
    unit: "A"
  },
  {
    id: 10,
    partName: "t2设备开关-压接1",
    identifyType: "开关",
    deviceType: "开关",
    warningType: "外观异常",
    unit: ""
  }
];

const ledgerTableColumns: DataTableColumn[] = [
  {
    type: "selection"
  },
  {
    key: "partName",
    title: "部件名称"
  },
  {
    key: "identifyType",
    title: "识别类型"
  },
  {
    key: "deviceType",
    title: "设备类型"
  },
  {
    key: "alarmType",
    title: "告警类型"
  },
  {
    key: "unit",
    title: "单位"
  }
];
</script>
