<template>
  <div
    :class="
      clsx('flex flex-col space-y-2 rounded-lg border border-primary-1 bg-[var(--card-visual-bg)] p-2', {
        'h-110': displayMode === 'default',
        'h-full': displayMode === 'fullScreen'
      })
    "
  >
    <div class="flex items-center justify-between">
      <div>
        <n-tabs default-value="1" class="[&>.n-tab-pane]:hidden">
          <n-tab-pane name="1" tab="巡检台账"></n-tab-pane>
          <n-tab-pane name="2" tab="机器人状态"></n-tab-pane>
          <n-tab-pane name="3" tab="实时信息"></n-tab-pane>
        </n-tabs>
      </div>
      <div class="flex items-center space-x-2">
        <div class="flex space-x-2">
          <span
            :class="
              clsx('text-sm', {
                'text-primary-2': displayMode === 'default',
                'text-[#18a058]': displayMode === 'fullScreen'
              })
            "
          >
            自动切换
          </span>
          <n-switch></n-switch>
        </div>
        <maximize-icon v-if="displayMode !== 'fullScreen'" class="size-4 cursor-pointer" @click="handleFullScreen" />
        <minimize-icon v-else class="size-4 cursor-pointer" @click="handleExitFullScreen" />
      </div>
    </div>
    <div class="flex-1">
      <n-data-table
        size="small"
        :bordered="false"
        :columns="columns"
        :data="data"
        flex-height
        :style="{ height: '100%' }"
        :pagination="{ page: 1 }"
      ></n-data-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { h } from "vue";
import { MaximizeIcon, MinimizeIcon } from "lucide-vue-next";
import { type DataTableColumn, type DataTableRowData, NButton } from "naive-ui";
import { clsx } from "clsx";

interface Props {
  displayMode?: "default" | "fullScreen";
}

withDefaults(defineProps<Props>(), {
  displayMode: "default"
});

const emits = defineEmits<{
  fullScreen: [componentName: string, props: Props];
  exitFullScreen: [];
}>();

const columns: DataTableColumn[] = [
  {
    key: "module",
    title: "模块",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: "function",
    title: "功能",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: "warning",
    title: "警告级别",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: "error",
    title: "异常描述",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: "createTime",
    title: "创建时间",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: "checkTime",
    title: "审核时间",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: "action",
    title: "操作",
    align: "center",
    render: () => {
      return [h(NButton, {}, { default: () => "审核" })];
    }
  }
];

const handleFullScreen = () => {
  emits("fullScreen", "StatisticalData", { displayMode: "fullScreen" });
};

const handleExitFullScreen = () => {
  emits("exitFullScreen");
};

const data: DataTableRowData[] = Array(20).fill({
  module: "充电房",
  function: "功能",
  warning: 1,
  error: "异常描述异常描述",
  createTime: "2024-11-29 11:24:13",
  checkTime: "2024-11-29 11:24:13"
});
</script>
