<template>
  <header
    class="relative flex h-20 items-center justify-between border-b border-slate-700/50 bg-slate-800/90 px-4 text-white backdrop-blur-sm lg:px-8"
  >
    <!-- 左侧：机器狗信息和环境数据 -->
    <div class="flex flex-shrink-0 items-center space-x-2">
      <!-- 机器狗标识 -->
      <div class="flex items-center space-x-2 lg:space-x-3">
        <bot-icon class="h-5 w-5 text-blue-400 lg:h-6 lg:w-6" />
        <span class="hidden text-lg font-semibold text-slate-200 sm:block lg:text-xl">机器狗01</span>
        <span class="text-sm font-semibold text-slate-200 sm:hidden lg:text-xl">狗01</span>
      </div>

      <!-- 环境数据 -->
      <div class="flex items-center space-x-2 lg:space-x-3">
        <!-- 温度 -->
        <div class="flex items-center space-x-1 rounded-lg bg-slate-700/50 px-2 py-1 lg:space-x-2 lg:px-3 lg:py-1.5">
          <thermometer-icon class="h-3 w-3 text-orange-400 lg:h-4 lg:w-4" />
          <span class="hidden text-xs text-slate-300 md:block lg:text-sm">温度</span>
          <span class="text-xs font-semibold text-orange-400 lg:text-sm">
            <n-number-animation :from="0" :to="wsDataStore.robotWeatherData.temperature" :duration="1000" :precision="2" />°
          </span>
        </div>

        <!-- 湿度 -->
        <div class="flex items-center space-x-1 rounded-lg bg-slate-700/50 px-2 py-1 lg:space-x-2 lg:px-3 lg:py-1.5">
          <droplets-icon class="h-3 w-3 text-blue-400 lg:h-4 lg:w-4" />
          <span class="hidden text-xs text-slate-300 md:block lg:text-sm">湿度</span>
          <span class="text-xs font-semibold text-blue-400 lg:text-sm">
            <n-number-animation :from="0" :to="wsDataStore.robotWeatherData.humidity" :duration="1000" />%
          </span>
        </div>

        <!-- 风力 - 在小屏幕上隐藏 -->
        <div class="hidden items-center space-x-2 rounded-lg bg-slate-700/50 px-3 py-1.5 lg:flex">
          <wind-icon class="h-4 w-4 text-green-400" />
          <span class="text-sm text-slate-300">风速</span>
          <span class="text-sm font-semibold text-green-400">
            <n-number-animation :from="0" :to="wsDataStore.robotWeatherData.windspeed" :duration="1000" :precision="2" />(m/s)
          </span>
        </div>
      </div>
    </div>

    <!-- 中间：系统标题 - 绝对居中，响应式字体 -->
    <div class="absolute left-1/2 hidden -translate-x-1/2 transform md:block">
      <h1 class="text-lg font-bold whitespace-nowrap text-slate-200 lg:text-2xl">{{ title }}</h1>
    </div>

    <!-- 右侧：菜单按钮和用户信息 -->
    <div class="flex flex-shrink-0 items-center space-x-3">
      <!-- 菜单按钮 -->
      <Button
        @click="$emit('openMenu')"
        variant="ghost"
        size="sm"
        class="border border-slate-600/50 bg-slate-700/50 text-slate-300 hover:bg-slate-600/50 hover:text-slate-100"
      >
        <Menu class="h-4 w-4" />
      </Button>

      <!-- 用户信息 -->
      <dropdown-menu>
        <dropdown-menu-trigger
          class="flex items-center space-x-3 rounded-lg p-2 transition-colors duration-200 hover:bg-slate-700/50"
        >
          <!-- 用户头像 -->
          <div class="flex h-8 w-8 items-center justify-center rounded-full">
            <!-- <span class="text-sm font-medium text-white">管</span> -->
            <UserIcon />
          </div>
          <!-- 用户名 -->
          <span class="text-sm font-medium text-slate-200">{{ authStore.role.name }}</span>
          <chevron-down-icon class="h-4 w-4 text-slate-400" />
        </dropdown-menu-trigger>
        <dropdown-menu-content class="border-slate-700 bg-slate-800">
          <dropdown-menu-item @click="handleLogout" class="text-slate-200 hover:bg-slate-700">
            <span>退出登录</span>
          </dropdown-menu-item>
        </dropdown-menu-content>
      </dropdown-menu>
    </div>

    <!-- 退出登录确认对话框 -->
    <Dialog v-model:open="showLogoutDialog">
      <DialogContent
        class="max-w-md border-slate-700/50 bg-slate-800/95 text-slate-200 backdrop-blur-sm [&>button]:border [&>button]:border-slate-600/50 [&>button]:bg-slate-700/50 [&>button]:text-slate-300 [&>button]:hover:bg-slate-600/50 [&>button]:hover:text-slate-100"
      >
        <DialogHeader class="pb-6 text-center">
          <div class="mb-4 flex items-center justify-center">
            <div class="flex h-12 w-12 items-center justify-center rounded-full bg-red-500/20">
              <AlertTriangle class="h-6 w-6 text-red-400" />
            </div>
          </div>
          <DialogTitle class="text-xl font-semibold text-slate-200">退出登录</DialogTitle>
          <DialogDescription class="mt-2 text-sm text-slate-400"
            >确定要退出当前账户吗？退出后需要重新登录才能访问系统。</DialogDescription
          >
        </DialogHeader>

        <DialogFooter class="flex flex-col-reverse gap-3 sm:flex-row">
          <Button
            variant="outline"
            @click="cancelLogout"
            class="flex-1 border-slate-500 bg-slate-700/30 text-slate-200 hover:border-slate-400 hover:bg-slate-600/50 hover:text-slate-100"
          >
            取消
          </Button>
          <Button @click="confirmLogout" class="flex-1 bg-red-600 font-medium text-white hover:bg-red-700">
            <LogOut class="mr-2 h-4 w-4" />
            确定退出
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </header>
</template>

<script setup lang="ts">
import {
  ChevronDownIcon,
  BotIcon,
  ThermometerIcon,
  DropletsIcon,
  WindIcon,
  CompassIcon,
  Menu,
  LogOut,
  AlertTriangle,
  UserIcon
} from "lucide-vue-next";
import { useRouter } from "vue-router";
import { onMounted, ref } from "vue";
// ==================== 导入依赖 ====================
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogDescription, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { useAuthStore } from "@/store/modules/auth";
import { useWsDataStore } from "@/store/modules/wsData";
import { RoutePath } from "@/constants";

import type { RobotNewWeatherRes } from "@/api/interface/visual";
import { robotNewWeatherApi } from "@/api/modules/visual";

defineEmits<{
  openMenu: [];
}>();

const title = import.meta.env.VITE_GLOBAL_APP_TITLE;

const router = useRouter();
const authStore = useAuthStore();
const wsDataStore = useWsDataStore();

// 退出登录确认对话框状态
const showLogoutDialog = ref(false);
const robotWeatherData = ref<RobotNewWeatherRes>({
  id: "",
  substation: "",
  robotSn: "",
  temperature: 0,
  humidity: 0,
  windSpeed: 0,
  windDirection: "",
  atmPressure: 0,
  rainAmount: 0,
  time: ""
});

// 打开退出登录确认对话框
const handleLogout = () => {
  showLogoutDialog.value = true;
};

// 确认退出登录
const confirmLogout = () => {
  authStore.setToken("");
  showLogoutDialog.value = false;
  router.replace(RoutePath.LOGIN);
};

// 取消退出登录
const cancelLogout = () => {
  showLogoutDialog.value = false;
};

const getRobotWeather = async () => {
  const res = await robotNewWeatherApi();
  robotWeatherData.value = res.data;
};

onMounted(() => {
  getRobotWeather();
});
</script>
