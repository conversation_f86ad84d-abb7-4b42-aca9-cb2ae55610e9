<template>
  <TooltipProvider>
    <div class="grid h-full grid-cols-3 gap-4">
      <!-- 车体信息 -->
      <div class="flex h-full flex-col overflow-hidden rounded-lg border border-slate-700/50 bg-slate-800/50">
        <div class="flex items-center gap-3 border-b border-slate-700/50 bg-slate-700/30 p-4">
          <div class="h-2 w-2 rounded-full bg-blue-500"></div>
          <Car class="h-4 w-4 text-blue-400" />
          <h3 class="text-sm font-medium text-slate-200">车体信息</h3>
          <div class="ml-auto">
            <div class="h-2 w-2 animate-pulse rounded-full bg-green-500"></div>
          </div>
        </div>
        <div class="flex-1 overflow-hidden">
          <ScrollArea class="h-full">
            <div class="space-y-3 p-4">
              <div
                v-for="item in wsDataStore.vehicleBodyList"
                :key="item.id"
                class="flex items-center justify-between border-b border-slate-700/30 py-2 last:border-b-0"
              >
                <span class="min-w-0 flex-shrink-0 text-xs font-medium text-slate-400">{{ item.label }}</span>
                <Tooltip>
                  <TooltipTrigger as-child>
                    <p class="ml-3 cursor-default truncate font-mono text-xs text-slate-200">
                      <span>{{ item.value || "-" }}</span>
                      <span v-if="item.tag === 'speed'">m/s</span>
                    </p>
                  </TooltipTrigger>
                  <TooltipContent class="text-slate-200">
                    <p class="text-sm">
                      <span>{{ item.value || "-" }}</span>
                      <span v-if="item.tag === 'speed'">m/s</span>
                    </p>
                  </TooltipContent>
                </Tooltip>
                <!-- <span class="ml-3 font-mono text-xs text-slate-200">{{ item.value || "-" }}</span> -->
              </div>
            </div>
          </ScrollArea>
        </div>
      </div>

      <!-- 云台信息 -->
      <div class="flex h-full flex-col overflow-hidden rounded-lg border border-slate-700/50 bg-slate-800/50">
        <div class="flex items-center gap-3 border-b border-slate-700/50 bg-slate-700/30 p-4">
          <div class="h-2 w-2 rounded-full bg-green-500"></div>
          <Camera class="h-4 w-4 text-green-400" />
          <h3 class="text-sm font-medium text-slate-200">云台信息</h3>
          <div class="ml-auto">
            <div class="h-2 w-2 animate-pulse rounded-full bg-green-500"></div>
          </div>
        </div>
        <div class="flex-1 overflow-hidden">
          <ScrollArea class="h-full">
            <div class="space-y-3 p-4">
              <div
                v-for="item in wsDataStore.ptzInformationList"
                :key="item.id"
                class="flex items-center justify-between border-b border-slate-700/30 py-2 last:border-b-0"
              >
                <span class="min-w-0 flex-shrink-0 text-xs font-medium text-slate-400">{{ item.label }}</span>
                <Tooltip>
                  <TooltipTrigger as-child>
                    <span class="ml-3 cursor-default truncate font-mono text-xs text-slate-200">{{ item.value || "-" }}</span>
                  </TooltipTrigger>
                  <TooltipContent class="text-slate-200">
                    <p class="text-sm">{{ item.value || "-" }}</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </div>
          </ScrollArea>
        </div>
      </div>

      <!-- 充电房信息 -->
      <div class="flex h-full flex-col overflow-hidden rounded-lg border border-slate-700/50 bg-slate-800/50">
        <div class="flex items-center gap-3 border-b border-slate-700/50 bg-slate-700/30 p-4">
          <div class="h-2 w-2 rounded-full bg-orange-500"></div>
          <Battery class="h-4 w-4 text-orange-400" />
          <h3 class="text-sm font-medium text-slate-200">充电房信息</h3>
          <div class="ml-auto">
            <div class="h-2 w-2 animate-pulse rounded-full bg-green-500"></div>
          </div>
        </div>
        <div class="flex-1 overflow-hidden">
          <ScrollArea class="h-full">
            <div class="space-y-3 p-4">
              <div
                v-for="item in wsDataStore.chargingRoomList"
                :key="item.id"
                class="flex items-center justify-between border-b border-slate-700/30 py-2 last:border-b-0"
              >
                <span class="min-w-0 flex-shrink-0 text-xs font-medium text-slate-400">{{ item.label }}</span>
                <Tooltip>
                  <TooltipTrigger as-child>
                    <p class="ml-3 cursor-default truncate font-mono text-xs text-slate-200">
                      <span>{{ item.value || "-" }}</span>
                      <span v-if="item.tag === 'battery'">%</span>
                    </p>
                  </TooltipTrigger>
                  <TooltipContent class="text-slate-200">
                    <p class="text-sm">
                      <span>{{ item.value || "-" }}</span>
                      <span v-if="item.tag === 'battery'">%</span>
                    </p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </div>
          </ScrollArea>
        </div>
      </div>
    </div>
  </TooltipProvider>
</template>

<script setup lang="ts">
// ==================== 导入依赖 ====================
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Car, Camera, Battery } from "lucide-vue-next";
import { useWsDataStore } from "@/store/modules/wsData";

const wsDataStore = useWsDataStore();
// ==================== 状态定义 ====================
// 充电房信息列表
const chargingRoomList = [
  { id: 1, label: "电量", value: "0.00" },
  { id: 2, label: "电压", value: "0.00" },
  { id: 3, label: "电流", value: "0.00" },
  { id: 4, label: "心跳时间", value: "" },
  { id: 5, label: "充电房门", value: "" },
  { id: 6, label: "在充电房", value: "" },
  { id: 7, label: "充电头接触", value: "" },
  { id: 8, label: "充电桩带电", value: "" }
];
</script>
