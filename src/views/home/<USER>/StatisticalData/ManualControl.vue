<template>
  <div class="flex h-full flex-col rounded-lg border border-slate-700/50 bg-slate-800/30">
    <!-- 标题栏 -->
    <header class="flex items-center justify-between border-b border-slate-700/50 bg-slate-700/30 p-4">
      <div class="flex items-center gap-3">
        <div class="h-2 w-2 rounded-full bg-blue-500"></div>
        <Settings class="h-5 w-5 text-blue-400" />
        <h3 class="text-lg font-semibold text-slate-200">机械臂控制</h3>
      </div>
      <Button
        variant="outline"
        size="sm"
        @click="toggleOperationHistory"
        class="border-blue-500/50 bg-blue-500/10 text-blue-400 hover:border-blue-400 hover:bg-blue-500/20 hover:text-blue-300"
      >
        <History class="mr-2 h-4 w-4" />
        {{ showHistory ? "隐藏历史" : "操作历史" }}
      </Button>
    </header>

    <!-- 主要内容区域 -->
    <div class="flex-1 overflow-hidden">
      <ScrollArea class="h-full">
        <div class="space-y-8 p-6">
          <!-- 主要控制区域 -->
          <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <!-- 方向控制区域 -->
            <div class="h-full rounded-lg border border-slate-700/30 bg-slate-800/50 p-6">
              <div class="mb-6 flex items-center gap-2">
                <Navigation class="h-4 w-4 text-blue-400" />
                <h4 class="text-sm font-medium text-slate-200">方向控制</h4>
              </div>
              <div class="mx-auto grid max-w-48 grid-cols-3 gap-3">
                <!-- 第一行：前 -->
                <div></div>
                <Button
                  variant="outline"
                  size="default"
                  :disabled="isExecuting !== null"
                  @click="executeRoboticArmCommand('前')"
                  class="h-12 w-12 border-blue-500/50 p-0 text-blue-400 transition-all duration-200 hover:border-blue-400 hover:bg-blue-500/10 hover:text-blue-300 disabled:opacity-50"
                >
                  <ArrowUpIcon class="h-5 w-5" />
                </Button>
                <div></div>

                <!-- 第二行：左、复位、右 -->
                <Button
                  variant="outline"
                  size="default"
                  :disabled="isExecuting !== null"
                  @click="executeRoboticArmCommand('左')"
                  class="h-12 w-12 border-blue-500/50 p-0 text-blue-400 transition-all duration-200 hover:border-blue-400 hover:bg-blue-500/10 hover:text-blue-300 disabled:opacity-50"
                >
                  <ArrowLeftIcon class="h-5 w-5" />
                </Button>
                <Button
                  variant="outline"
                  size="default"
                  :disabled="isExecuting !== null"
                  @click="confirmReset"
                  class="h-12 w-12 border-orange-500/50 p-0 text-orange-400 transition-all duration-200 hover:border-orange-400 hover:bg-orange-500/10 hover:text-orange-300 disabled:opacity-50"
                >
                  <RotateCcw class="h-5 w-5" />
                </Button>
                <Button
                  variant="outline"
                  size="default"
                  :disabled="isExecuting !== null"
                  @click="executeRoboticArmCommand('右')"
                  class="h-12 w-12 border-blue-500/50 p-0 text-blue-400 transition-all duration-200 hover:border-blue-400 hover:bg-blue-500/10 hover:text-blue-300 disabled:opacity-50"
                >
                  <ArrowRightIcon class="h-5 w-5" />
                </Button>

                <!-- 第三行：后 -->
                <div></div>
                <Button
                  variant="outline"
                  size="default"
                  :disabled="isExecuting !== null"
                  @click="executeRoboticArmCommand('后')"
                  class="h-12 w-12 border-blue-500/50 p-0 text-blue-400 transition-all duration-200 hover:border-blue-400 hover:bg-blue-500/10 hover:text-blue-300 disabled:opacity-50"
                >
                  <ArrowDownIcon class="h-5 w-5" />
                </Button>
                <div></div>
              </div>
            </div>

            <!-- 垂直控制 -->
            <div class="h-full rounded-lg border border-slate-700/30 bg-slate-800/50 p-6">
              <div class="mb-6 flex items-center gap-2">
                <MoveVertical class="h-4 w-4 text-green-400" />
                <h4 class="text-sm font-medium text-slate-200">垂直控制</h4>
              </div>
              <div class="mx-auto flex h-[calc(100%-4rem)] max-w-32 flex-col justify-center gap-3">
                <Button
                  variant="outline"
                  size="default"
                  :disabled="isExecuting !== null"
                  @click="executeRoboticArmCommand('上')"
                  class="h-12 border-green-500/50 text-green-400 transition-all duration-200 hover:border-green-400 hover:bg-green-500/10 hover:text-green-300 disabled:opacity-50"
                >
                  <ArrowUpIcon class="mr-2 h-4 w-4" />
                  上升
                </Button>
                <Button
                  variant="outline"
                  size="default"
                  :disabled="isExecuting !== null"
                  @click="executeRoboticArmCommand('下')"
                  class="h-12 border-green-500/50 text-green-400 transition-all duration-200 hover:border-green-400 hover:bg-green-500/10 hover:text-green-300 disabled:opacity-50"
                >
                  <ArrowDownIcon class="mr-2 h-4 w-4" />
                  下降
                </Button>
              </div>
            </div>
          </div>

          <!-- 旋转和夹爪控制区域 -->
          <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <!-- 旋转控制 -->
            <div class="h-full rounded-lg border border-slate-700/30 bg-slate-800/50 p-6">
              <div class="mb-6 flex items-center gap-2">
                <RotateCw class="h-4 w-4 text-purple-400" />
                <h4 class="text-sm font-medium text-slate-200">旋转控制</h4>
              </div>
              <div class="grid grid-cols-2 gap-3">
                <Button
                  variant="outline"
                  size="default"
                  :disabled="isExecuting !== null"
                  @click="executeRoboticArmCommand('左旋转')"
                  class="h-12 border-purple-500/50 text-purple-400 transition-all duration-200 hover:border-purple-400 hover:bg-purple-500/10 hover:text-purple-300 disabled:opacity-50"
                >
                  <RotateCcw class="mr-2 h-4 w-4" />
                  左旋
                </Button>
                <Button
                  variant="outline"
                  size="default"
                  :disabled="isExecuting !== null"
                  @click="executeRoboticArmCommand('右旋转')"
                  class="h-12 border-purple-500/50 text-purple-400 transition-all duration-200 hover:border-purple-400 hover:bg-purple-500/10 hover:text-purple-300 disabled:opacity-50"
                >
                  <RotateCw class="mr-2 h-4 w-4" />
                  右旋
                </Button>
                <Button
                  variant="outline"
                  size="default"
                  :disabled="isExecuting !== null"
                  @click="executeRoboticArmCommand('左面')"
                  class="h-12 border-purple-500/50 text-purple-400 transition-all duration-200 hover:border-purple-400 hover:bg-purple-500/10 hover:text-purple-300 disabled:opacity-50"
                >
                  <ArrowLeftIcon class="mr-2 h-4 w-4" />
                  左面
                </Button>
                <Button
                  variant="outline"
                  size="default"
                  :disabled="isExecuting !== null"
                  @click="executeRoboticArmCommand('右面')"
                  class="h-12 border-purple-500/50 text-purple-400 transition-all duration-200 hover:border-purple-400 hover:bg-purple-500/10 hover:text-purple-300 disabled:opacity-50"
                >
                  <ArrowRightIcon class="mr-2 h-4 w-4" />
                  右面
                </Button>
              </div>
            </div>

            <!-- 夹爪控制 -->
            <div class="h-full rounded-lg border border-slate-700/30 bg-slate-800/50 p-6">
              <div class="mb-6 flex items-center gap-2">
                <GrabIcon class="h-4 w-4 text-yellow-400" />
                <h4 class="text-sm font-medium text-slate-200">夹爪控制</h4>
              </div>
              <div class="flex h-[calc(100%-4rem)] flex-col justify-center gap-3">
                <Button
                  variant="outline"
                  size="default"
                  :disabled="isExecuting !== null"
                  @click="executeRoboticArmCommand('加紧夹爪')"
                  class="h-12 border-yellow-500/50 text-yellow-400 transition-all duration-200 hover:border-yellow-400 hover:bg-yellow-500/10 hover:text-yellow-300 disabled:opacity-50"
                >
                  <GrabIcon class="mr-2 h-4 w-4" />
                  加紧夹爪
                </Button>
                <Button
                  variant="outline"
                  size="default"
                  :disabled="isExecuting !== null"
                  @click="executeRoboticArmCommand('放松夹爪')"
                  class="h-12 border-cyan-500/50 text-cyan-400 transition-all duration-200 hover:border-cyan-400 hover:bg-cyan-500/10 hover:text-cyan-300 disabled:opacity-50"
                >
                  <Hand class="mr-2 h-4 w-4" />
                  放松夹爪
                </Button>
              </div>
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>
  </div>

  <!-- 操作历史侧边栏 -->
  <Sheet :open="showHistory" @update:open="showHistory = $event">
    <SheetContent
      class="flex w-[400px] flex-col border-slate-700 bg-slate-900/95 backdrop-blur-sm [&>button]:border [&>button]:border-slate-600/50 [&>button]:bg-slate-700/50 [&>button]:text-slate-300 [&>button]:hover:bg-slate-600/50 [&>button]:hover:text-slate-100"
    >
      <SheetHeader class="flex-shrink-0 border-b border-slate-700/50 pb-6">
        <SheetTitle class="flex items-center gap-2 text-lg font-semibold text-slate-100">
          <Clock class="h-5 w-5 text-blue-400" />
          操作历史
        </SheetTitle>
      </SheetHeader>

      <div class="flex-1 overflow-hidden py-6">
        <ScrollArea class="h-full">
          <div class="space-y-3 pr-4">
            <div
              v-for="(record, index) in operationHistory"
              :key="index"
              class="rounded-lg border border-slate-700/30 bg-slate-800/50 p-4"
            >
              <div class="mb-2 flex items-center justify-between">
                <span class="font-mono text-xs text-slate-400">{{ record.time }}</span>
                <span
                  :class="[
                    'rounded-full px-2 py-1 text-xs font-medium',
                    record.status === 'success'
                      ? 'border border-green-500/30 bg-green-500/20 text-green-400'
                      : record.status === 'error'
                        ? 'border border-red-500/30 bg-red-500/20 text-red-400'
                        : 'border border-yellow-500/30 bg-yellow-500/20 text-yellow-400'
                  ]"
                >
                  {{ record.status === "success" ? "✓ 成功" : record.status === "error" ? "✗ 失败" : "⏳ 执行中" }}
                </span>
              </div>
              <div class="text-sm font-medium text-slate-200">命令：{{ record.command }}</div>
            </div>

            <div v-if="operationHistory.length === 0" class="py-8 text-center">
              <History class="mx-auto mb-3 h-12 w-12 text-slate-600" />
              <p class="text-sm text-slate-500">暂无操作记录</p>
              <p class="mt-1 text-xs text-slate-600">执行命令后将在此显示历史记录</p>
            </div>
          </div>
        </ScrollArea>
      </div>

      <SheetFooter class="flex-shrink-0 border-t border-slate-700/50 pt-6">
        <Button
          variant="outline"
          @click="clearHistory"
          class="w-full border-slate-500 bg-slate-700/30 text-slate-200 hover:border-slate-400 hover:bg-slate-600/50 hover:text-slate-100"
        >
          <Trash2 class="mr-2 h-4 w-4" />
          清空历史记录
        </Button>
      </SheetFooter>
    </SheetContent>
  </Sheet>
</template>

<script setup lang="ts">
// ==================== 导入依赖 ====================
import { ref } from "vue";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetFooter } from "@/components/ui/sheet";
import {
  ArrowUpIcon,
  ArrowLeftIcon,
  ArrowRightIcon,
  ArrowDownIcon,
  RotateCw,
  RotateCcw,
  GrabIcon,
  Hand,
  Settings,
  History,
  Clock,
  Navigation,
  MoveVertical,
  Trash2
} from "lucide-vue-next";
// ==================== 页面组件导入 ====================

// ==================== 全局组件导入 ====================

// ==================== 其他导入项 ====================

// ==================== TypeScript ====================
type RoboticArmCommand =
  | "前"
  | "后"
  | "左"
  | "右"
  | "上"
  | "下"
  | "左旋转"
  | "右旋转"
  | "左面"
  | "右面"
  | "复位"
  | "抓取"
  | "释放"
  | "加紧夹爪"
  | "放松夹爪";

interface OperationRecord {
  time: string;
  command: RoboticArmCommand;
  status: "success" | "error" | "pending";
}
// ==================== vue宏定义 ====================

// ==================== 钩子定义 ====================

// ==================== 状态定义 ====================
// 当前执行的命令
const isExecuting = ref<RoboticArmCommand | null>(null);
// 是否显示操作历史
const showHistory = ref(false);
// 操作历史记录
const operationHistory = ref<OperationRecord[]>([]);

// ==================== 方法定义 ====================
/**
 * 添加操作记录
 */
const addOperationRecord = (command: RoboticArmCommand, status: "success" | "error" | "pending") => {
  const record: OperationRecord = {
    time: new Date().toLocaleTimeString(),
    command,
    status
  };

  operationHistory.value.unshift(record);

  // 只保留最近10条记录
  if (operationHistory.value.length > 10) {
    operationHistory.value = operationHistory.value.slice(0, 10);
  }
};

/**
 * 执行机械臂控制命令
 * @param command 控制命令
 */
const executeRoboticArmCommand = async (command: RoboticArmCommand) => {
  try {
    isExecuting.value = command;
    addOperationRecord(command, "pending");

    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    // TODO: 这里应该调用实际的机械臂控制API
    // const result = await roboticArmAPI.executeCommand(command);

    // 模拟成功/失败
    const isSuccess = Math.random() > 0.1; // 90% 成功率

    if (isSuccess) {
      console.log(`命令 "${command}" 执行成功`);
      // 更新最后一条记录的状态
      if (operationHistory.value.length > 0) {
        operationHistory.value[0].status = "success";
      }
    } else {
      throw new Error("命令执行失败");
    }
  } catch (error) {
    console.error(`命令 "${command}" 执行失败:`, error);
    // 更新最后一条记录的状态
    if (operationHistory.value.length > 0) {
      operationHistory.value[0].status = "error";
    }
  } finally {
    isExecuting.value = null;
  }
};

/**
 * 确认复位操作
 */
const confirmReset = () => {
  if (confirm("确定要将机械臂复位到初始位置吗？此操作不可撤销。")) {
    executeRoboticArmCommand("复位");
  }
};

/**
 * 切换操作历史显示
 */
const toggleOperationHistory = () => {
  showHistory.value = !showHistory.value;
};

/**
 * 清空操作历史
 */
const clearHistory = () => {
  operationHistory.value = [];
};

// ==================== 生命周期 ====================
</script>
