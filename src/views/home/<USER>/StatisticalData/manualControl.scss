.robotic-arm-control {
  display: flex;
  height: 100%;
  flex-direction: column;
  outline: none;

  .control-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--color-primary-2);

    .header-left {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .control-title {
        margin: 0;
        font-size: 1.125rem;
        font-weight: 500;
      }
    }

    .header-right {
      display: flex;
      gap: 0.5rem;
    }
  }

  .control-content {
    display: flex;
    flex: 1;
    gap: 1rem;
    overflow-y: auto;

    .history-panel {
      width: 200px;
      background: var(--card-visual-bg);
      border: 1px solid var(--border-color);
      border-radius: 6px;
      padding: 0.75rem;
      overflow-y: auto;

      h4 {
        margin: 0 0 0.5rem 0;
        font-size: 0.875rem;
        color: var(--text-color-2);
      }

      .history-list {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;

        .history-item {
          display: flex;
          flex-direction: column;
          gap: 0.125rem;
          padding: 0.25rem;
          background: var(--fill-color);
          border-radius: 4px;
          font-size: 0.75rem;

          .history-time {
            color: var(--text-color-3);
          }

          .history-command {
            font-weight: 500;
          }

          .history-status {
            font-size: 0.625rem;
            padding: 0.125rem 0.25rem;
            border-radius: 2px;
            text-align: center;

            &.success {
              background: #18a058;
              color: white;
            }

            &.error {
              background: #d03050;
              color: white;
            }

            &.pending {
              background: #f0a020;
              color: white;
            }
          }
        }

        .no-history {
          text-align: center;
          color: var(--text-color-3);
          font-size: 0.75rem;
          padding: 1rem 0;
        }
      }
    }

    .main-controls,
    .vertical-controls,
    .rotation-controls {
      .section-title {
        margin: 0 0 0.75rem 0;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-color-2);
        text-align: center;
      }
    }

    .main-controls {
      flex: 1;
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: auto auto;
      gap: 1.5rem;
      padding: 0.5rem;

      /* 方向控制 */
      .movement-controls {
        grid-column: 1 / 2;
        grid-row: 1 / 2;

        .direction-pad {
          display: grid;
          grid-template-areas:
            ". A ."
            "B C D"
            ". E .";
          justify-content: center;
          gap: 0.5rem;

          .direction-btn {
            width: 80px;
            height: 40px;
            font-size: 0.875rem;
            transition: all 0.2s ease;

            &:nth-child(1) {
              grid-area: A;
            }

            &:nth-child(2) {
              grid-area: B;
            }

            &:nth-child(3) {
              grid-area: C;
            }

            &:nth-child(4) {
              grid-area: D;
            }

            &:nth-child(5) {
              grid-area: E;
            }

            &:hover:not(:disabled) {
              transform: scale(1.05);
            }

            .reset-btn {
              font-weight: 600;
              background: linear-gradient(135deg, #f0a020, #d4851c);
            }
          }
        }
      }

      /* 垂直控制 */
      .vertical-controls {
        grid-column: 2 / 3;
        grid-row: 1 / 2;

        .vertical-buttons {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
          align-items: center;

          .vertical-btn {
            width: 100px;
            height: 45px;
            font-size: 0.875rem;
            transition: all 0.2s ease;

            &:hover:not(:disabled) {
              transform: scale(1.05);
            }
          }
        }
      }

      .rotation-controls {
        grid-column: 1 / 2;
        grid-row: 2 / 3;

        .rotation-buttons {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 0.5rem;

          .rotation-btn {
            height: 40px;
            font-size: 0.875rem;
            transition: all 0.2s ease;

            &:hover:not(:disabled) {
              transform: scale(1.05);
            }
          }
        }
      }

      /* 夹爪控制 */
      .gripper-controls {
        grid-column: 2 / 3;
        grid-row: 2 / 3;

        .gripper-buttons {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;

          .gripper-btn {
            height: 40px;
            font-size: 0.875rem;
            transition: all 0.2s ease;

            &:hover:not(:disabled) {
              transform: scale(1.05);
            }
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    .control-content {
      flex-direction: column;

      .history-panel {
        width: 100%;
        height: 120px;
      }
    }

    .main-controls {
      grid-template-columns: 1fr;
      grid-template-rows: auto auto auto auto;

      .movement-controls {
        grid-column: 1;
        grid-row: 1;
      }

      .vertical-controls {
        grid-column: 1;
        grid-row: 2;
      }

      .rotation-controls {
        grid-column: 1;
        grid-row: 3;
      }

      .gripper-controls {
        grid-column: 1;
        grid-row: 4;
      }

      .direction-btn,
      .vertical-btn,
      .rotation-btn,
      .gripper-btn {
        height: 36px;
        font-size: 0.75rem;
      }
    }
  }
}
