<template>
  <DataTable
    :columns="columns"
    :data="tableData"
    :loading="loading"
    :pagination="{ page: pageable.page, limit: pageable.limit, total: pageable.total }"
    :show-pagination="true"
    :show-page-size-selector="true"
    :page-size-options="pageSizeOptions"
    @page-change="handleCurrentChange"
    @page-size-change="handlePageSizeChange"
  />
</template>

<script setup lang="ts">
// ==================== 导入依赖 ====================
import { onMounted, h } from "vue";

// ==================== 组件导入 ====================
import DataTable from "@/components/common/DataTable.vue";

// ==================== 其他导入项 ====================
import { overallLedgerListAPi } from "@/api/modules/mock";
import { useTable } from "@/hooks";

// ==================== TypeScript ====================
interface LedgerData {
  alarmLevel: number;
  region?: string;
  device?: string;
  inspectionPoint?: string;
  detectionType?: string;
  value?: string;
  imageUrl?: string;
  time?: string;
  executionStatus?: string;
}

interface TableColumn {
  key: keyof LedgerData;
  title: string;
  width?: string;
  cellClass?: string;
  ellipsis?: boolean | { tooltip?: boolean };
  noWrap?: boolean;
  render?: (params: { rowData: LedgerData; index: number }) => any;
}

// ==================== vue宏定义 ====================

// ==================== 钩子定义 ====================
const { getTableList, loading, tableData, pageable, handleCurrentChange, handleSizeChange } = useTable(overallLedgerListAPi);

// ==================== 状态定义 ====================
const ALARM_LEVEL_MAP: { [key: number]: string } = {
  1: "正常",
  2: "异常"
};

// 分页选项
const pageSizeOptions = [10, 20, 50, 100];

// 表格列配置
const columns: TableColumn[] = [
  {
    key: "alarmLevel",
    title: "告警级别",
    width: "120px",
    noWrap: true,
    render: ({ rowData }) => {
      const level = rowData.alarmLevel || 1;
      const colorClass = level === 1 ? "bg-green-500/20 text-green-400" : "bg-red-500/20 text-red-400";
      return h(
        "span",
        {
          class: `rounded-full px-2 py-1 text-xs font-medium ${colorClass}`
        },
        ALARM_LEVEL_MAP[level]
      );
    }
  },
  {
    key: "region",
    title: "区域",
    ellipsis: true,
    cellClass: "text-xs text-slate-300"
  },
  {
    key: "device",
    title: "设备",
    ellipsis: true,
    cellClass: "text-xs text-slate-300"
  },
  {
    key: "inspectionPoint",
    title: "巡检点",
    ellipsis: true,
    cellClass: "text-xs text-slate-300"
  },
  {
    key: "detectionType",
    title: "识别类型",
    ellipsis: true,
    cellClass: "text-xs text-slate-300"
  },
  {
    key: "value",
    title: "数值",
    ellipsis: true,
    cellClass: "font-mono text-xs text-slate-200"
  },
  {
    key: "imageUrl",
    title: "操作",
    width: "100px",
    noWrap: true,
    render: ({ rowData }) => {
      return h(
        "button",
        {
          class:
            "rounded bg-blue-500/20 px-2 py-1 text-xs text-blue-400 transition-colors duration-150 hover:bg-blue-500/30",
          onClick: () => handleViewImage(rowData.imageUrl!)
        },
        "查看"
      );
    }
  },
  {
    key: "time",
    title: "时间",
    ellipsis: true,
    cellClass: "font-mono text-xs text-slate-300"
  },
  {
    key: "executionStatus",
    title: "执行状态",
    ellipsis: true,
    cellClass: "text-xs text-slate-300"
  }
];

// ==================== 方法定义 ====================

/**
 * 查看图片
 */
const handleViewImage = (imageUrl: string) => {
  console.log("查看图片:", imageUrl);
  window.open(imageUrl, "_blank");
};

/**
 * 编辑行数据
 */
const handleEdit = (row: LedgerData, index: number) => {
  console.log("编辑行数据:", row, index);
  // 这里可以打开编辑弹窗或跳转到编辑页面
};

/**
 * 处理每页条数变化
 */
const handlePageSizeChange = (value: number) => {
  handleSizeChange(value);
};

// ==================== 生命周期 ====================
onMounted(() => {
  getTableList();
});
</script>
