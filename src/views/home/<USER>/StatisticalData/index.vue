<template>
  <div
    :class="
      clsx(
        'hover:shadow-3xl flex h-full flex-col overflow-hidden rounded-2xl border border-slate-700/50 bg-slate-800/60 shadow-2xl backdrop-blur-sm transition-all duration-300 hover:border-slate-600/50 hover:bg-slate-800/80'
      )
    "
  >
    <!-- 标题栏 -->
    <div class="flex items-center justify-between border-b border-slate-700/50 bg-slate-700/30 p-4">
      <div class="flex items-center space-x-3">
        <div class="h-2 w-2 animate-pulse rounded-full bg-purple-400"></div>
        <div class="flex items-center space-x-4">
          <!-- 自定义标签页 -->
          <div class="flex space-x-1 rounded-lg bg-slate-600/50 p-1">
            <button
              v-for="tabConfig in tabConfigList"
              :key="tabConfig.key"
              :class="
                clsx('rounded-md px-3 py-1.5 text-xs font-medium transition-all duration-200', {
                  'bg-slate-500/80 text-slate-100 shadow-sm': activeTabKey === tabConfig.key,
                  'text-slate-300 hover:bg-slate-500/40 hover:text-slate-200': activeTabKey !== tabConfig.key
                })
              "
              @click="activeTabKey = tabConfig.key"
            >
              {{ tabConfig.label }}
            </button>
          </div>
        </div>
      </div>

      <div class="flex items-center space-x-3">
        <!-- 自动切换控制 -->
        <div class="flex items-center space-x-2 rounded-lg bg-slate-600/50 px-3 py-1.5">
          <div
            :class="
              clsx('h-2 w-2 rounded-full transition-colors duration-200', {
                'animate-pulse bg-green-400': autoSwitch,
                'bg-slate-500': !autoSwitch
              })
            "
          ></div>
          <span class="text-xs text-slate-300">
            {{ autoSwitch ? "自动切换" : "手动切换" }}
          </span>
          <button
            :class="
              clsx('relative inline-flex h-4 w-7 items-center rounded-full transition-colors duration-200', {
                'bg-blue-500': autoSwitch,
                'bg-slate-600': !autoSwitch
              })
            "
            @click="autoSwitch = !autoSwitch"
          >
            <span
              :class="
                clsx('inline-block h-3 w-3 transform rounded-full bg-white transition-transform duration-200', {
                  'translate-x-3.5': autoSwitch,
                  'translate-x-0.5': !autoSwitch
                })
              "
            ></span>
          </button>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="flex-1 overflow-hidden bg-slate-800/20 p-4" @mouseenter="mouseEnter = true" @mouseleave="mouseEnter = false">
      <!-- <Ledger v-if="activeTabKey === 'ledger'" /> -->
      <robot-status v-if="activeTabKey === 'robotStatus'" />
      <realtime-info v-if="activeTabKey === 'realtimeInfo'" />
      <manual-control v-if="activeTabKey === 'manualControl'" />
    </div>
  </div>
</template>

<script setup lang="ts">
// ==================== 导入依赖 ====================
import { reactive, ref, watchEffect, onUnmounted } from "vue";
import clsx from "clsx";
// ==================== 页面组件导入 ====================
// import Ledger from "./Ledger.vue";
import RobotStatus from "./RobotStatus.vue";
import RealtimeInfo from "./RealtimeInfo.vue";
import ManualControl from "./ManualControl.vue";
// ==================== 全局组件导入 ====================

// ==================== 其他导入项 ====================

// ==================== TypeScript ====================
interface Props {}

interface TabConfig {
  key: ActiveTabKeyType;
  label: string;
}

// type ActiveTabKeyType = "ledger" | "robotStatus" | "realtimeInfo" | "manualControl";
type ActiveTabKeyType = "robotStatus" | "realtimeInfo" | "manualControl";
// ==================== vue宏定义 ====================
withDefaults(defineProps<Props>(), {});
// ==================== 钩子定义 ====================

// ==================== 状态定义 ====================
const tabConfigList = reactive<TabConfig[]>([
  // { key: "ledger", label: "巡检台账" },
  { key: "robotStatus", label: "机器人状态" },
  { key: "realtimeInfo", label: "实时信息" },
  { key: "manualControl", label: "人工控制" }
]);

// const activeTabKey = ref<ActiveTabKeyType>("ledger");
const activeTabKey = ref<ActiveTabKeyType>("robotStatus");
// 是否自动切换
const autoSwitch = ref(false);
const mouseEnter = ref(false);
// 自动切换间隔时间（毫秒）
const AUTO_SWITCH_INTERVAL = 10 * 1000;
// 定时器引用
let autoSwitchTimer: NodeJS.Timeout | null = null;
// ==================== 方法定义 ====================

/**
 * 切换到下一个标签页
 */
const switchToNextTab = () => {
  const currentIndex = tabConfigList.findIndex(tab => tab.key === activeTabKey.value);
  const nextIndex = (currentIndex + 1) % tabConfigList.length;
  activeTabKey.value = tabConfigList[nextIndex].key;
};

/**
 * 启动自动切换定时器
 */
const startAutoSwitch = () => {
  if (autoSwitchTimer) {
    clearInterval(autoSwitchTimer);
  }
  autoSwitchTimer = setInterval(() => {
    if (autoSwitch.value && !mouseEnter.value) {
      switchToNextTab();
    }
  }, AUTO_SWITCH_INTERVAL);
};

/**
 * 停止自动切换定时器
 */
const stopAutoSwitch = () => {
  if (autoSwitchTimer) {
    clearInterval(autoSwitchTimer);
    autoSwitchTimer = null;
  }
};

/**
 * 处理自动切换状态变化
 */
const handleAutoSwitchChange = (value: boolean) => {
  if (value) {
    startAutoSwitch();
  } else {
    stopAutoSwitch();
  }
};

// 监听自动切换状态变化
watchEffect(() => {
  handleAutoSwitchChange(autoSwitch.value);
});

// ==================== 生命周期 ====================
onUnmounted(() => {
  stopAutoSwitch();
});
</script>

<style scoped></style>
