<template>
  <div class="h-full">
    <DataTable
      :columns="columns"
      :data="tableData"
      :loading="loading"
      :pagination="{ page: pageable.page, limit: pageable.limit, total: pageable.total }"
      :show-pagination="true"
      :show-page-size-selector="true"
      :page-size-options="[10, 20, 50, 100]"
      @page-change="handleCurrentChange"
      @page-size-change="handleSizeChange"
    />

    <!-- 审核机器人状态侧边栏 -->
    <Sheet :open="reviewVisible" @update:open="reviewVisible = $event">
      <SheetContent class="w-[600px] bg-slate-900/95 border-slate-700 backdrop-blur-sm">
        <SheetHeader class="pb-6 border-b border-slate-700/50">
          <SheetTitle class="text-xl font-semibold text-slate-100 flex items-center gap-2">
            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
            审核机器人状态
          </SheetTitle>
        </SheetHeader>

        <div class="space-y-6 py-6">
          <!-- 基本信息卡片 -->
          <div class="bg-slate-800/50 rounded-lg p-4 border border-slate-700/30">
            <h3 class="text-sm font-medium text-slate-200 mb-4 flex items-center gap-2">
              <div class="w-1 h-4 bg-blue-500 rounded-full"></div>
              基本信息
            </h3>
            <div class="grid grid-cols-2 gap-4">
              <!-- 机器人 -->
              <div class="space-y-2">
                <Label class="text-slate-400 text-xs font-medium uppercase tracking-wide">机器人</Label>
                <div class="p-3 bg-slate-700/30 border border-slate-600/50 rounded-md text-slate-200 text-sm font-medium">
                  {{ reviewForm.robot }}
                </div>
              </div>

              <!-- 模块 -->
              <div class="space-y-2">
                <Label class="text-slate-400 text-xs font-medium uppercase tracking-wide">模块</Label>
                <div class="p-3 bg-slate-700/30 border border-slate-600/50 rounded-md text-slate-200 text-sm font-medium">
                  {{ reviewForm.module }}
                </div>
              </div>

              <!-- 功能 -->
              <div class="space-y-2">
                <Label class="text-slate-400 text-xs font-medium uppercase tracking-wide">功能</Label>
                <div class="p-3 bg-slate-700/30 border border-slate-600/50 rounded-md text-slate-200 text-sm font-medium">
                  {{ reviewForm.function }}
                </div>
              </div>

              <!-- 告警类型 -->
              <div class="space-y-2">
                <Label class="text-slate-400 text-xs font-medium uppercase tracking-wide">告警类型</Label>
                <div class="p-3 bg-slate-700/30 border border-slate-600/50 rounded-md">
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-400 border border-yellow-500/30">
                    {{ reviewForm.warning }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 异常描述卡片 -->
          <div class="bg-slate-800/50 rounded-lg p-4 border border-slate-700/30">
            <h3 class="text-sm font-medium text-slate-200 mb-3 flex items-center gap-2">
              <div class="w-1 h-4 bg-red-500 rounded-full"></div>
              异常描述
            </h3>
            <div class="p-4 bg-slate-700/30 border border-slate-600/50 rounded-md text-slate-300 text-sm leading-relaxed min-h-[80px]">
              {{ reviewForm.error || '暂无异常描述' }}
            </div>
          </div>

          <!-- 审批意见卡片 -->
          <div class="bg-slate-800/50 rounded-lg p-4 border border-slate-700/30">
            <h3 class="text-sm font-medium text-slate-200 mb-3 flex items-center gap-2">
              <div class="w-1 h-4 bg-green-500 rounded-full"></div>
              审批意见
              <span class="text-xs text-red-400">*</span>
            </h3>
            <textarea
              v-model="reviewForm.approval"
              placeholder="请输入您的审批意见，包括处理建议、风险评估等..."
              class="w-full min-h-[120px] p-4 bg-slate-700/30 border border-slate-600/50 rounded-md text-slate-200 text-sm placeholder:text-slate-500 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
              rows="5"
            />
            <div class="mt-2 text-xs text-slate-500">
              建议包含：问题分析、处理方案、预防措施等
            </div>
          </div>
        </div>

        <SheetFooter class="gap-3 pt-6 border-t border-slate-700/50">
          <Button
            variant="ghost"
            @click="handleReviewCancel"
            class="flex-1 bg-slate-700/30 border border-slate-600/50 text-slate-300 hover:bg-slate-600/50 hover:text-slate-200 hover:border-slate-500/70 transition-all duration-200"
          >
            <span class="mr-2">✕</span>
            取消
          </Button>
          <Button
            @click="handleReviewConfirm"
            class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-blue-500/25 transition-all duration-200"
          >
            <span class="mr-2">✓</span>
            确认审核
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  </div>
</template>

<script setup lang="ts">
// ==================== 导入依赖 ====================
import { h, onMounted, ref } from "vue";
import { useMessage } from "naive-ui";
import _ from "lodash";

// ==================== 组件导入 ====================
import DataTable from "@/components/common/DataTable.vue";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetFooter,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";

// ==================== 其他导入项 ====================
import { overallRobotStatusAPi } from "@/api/modules/mock";
import { useTable } from "@/hooks";
// ==================== TypeScript ====================
interface RobotStatusData {
  id: string;
  module: string;
  function: string;
  warning: number;
  error: string;
  createTime: string;
  checkTime: string;
  robot: string;
  review: 1 | 2;
}

interface TableColumn<T = any> {
  key: keyof T | string;
  title: string;
  width?: string;
  cellClass?: string;
  ellipsis?: boolean | { tooltip?: boolean };
  noWrap?: boolean;
  render?: (params: { rowData: T; index: number }) => any;
  slot?: string;
}

type WarningType = { [key: number]: string };
// ==================== vue宏定义 ====================

// ==================== 钩子定义 ====================
const { getTableList, loading, tableData, pageable, handleCurrentChange, handleSizeChange } = useTable(overallRobotStatusAPi);

const message = useMessage();
// ==================== 状态定义 ====================
const WARNING_MAP: WarningType = {
  1: "一般告警",
  2: "严重告警",
  3: "紧急告警"
};

const initialReviewForm = {
  id: "",
  module: "",
  function: "",
  warning: "",
  error: "",
  approval: "",
  robot: ""
};

const reviewVisible = ref(false);
const reviewForm = ref(_.clone(initialReviewForm));

const columns: TableColumn<RobotStatusData>[] = [
  {
    key: "module",
    title: "模块",
    ellipsis: true,
    cellClass: "text-xs text-slate-300"
  },
  {
    key: "function",
    title: "功能",
    ellipsis: true,
    cellClass: "text-xs text-slate-300"
  },
  {
    key: "warning",
    title: "警告级别",
    width: "120px",
    noWrap: true,
    render: ({ rowData }) => {
      const level = rowData.warning;
      let colorClass = "bg-gray-500/20 text-gray-400"; // 默认颜色

      switch (level) {
        case 1:
          colorClass = "bg-yellow-500/20 text-yellow-400";
          break;
        case 2:
          colorClass = "bg-orange-500/20 text-orange-400";
          break;
        case 3:
          colorClass = "bg-red-500/20 text-red-400";
          break;
      }

      return h('span', {
        class: `rounded-full px-2 py-1 text-xs font-medium ${colorClass}`
      }, WARNING_MAP[level] || '未知');
    }
  },
  {
    key: "error",
    title: "异常描述",
    ellipsis: true,
    cellClass: "text-xs text-slate-300"
  },
  {
    key: "createTime",
    title: "创建时间",
    ellipsis: true,
    cellClass: "font-mono text-xs text-slate-300"
  },
  {
    key: "checkTime",
    title: "审核时间",
    ellipsis: true,
    cellClass: "font-mono text-xs text-slate-300"
  },
  {
    key: "robot",
    title: "机器人",
    ellipsis: true,
    cellClass: "text-xs text-slate-300"
  },
  {
    key: "action",
    title: "操作",
    width: "100px",
    noWrap: true,
    render: ({ rowData }) => {
      return rowData.review === 1
        ? h('button', {
            class: 'rounded bg-blue-500/20 px-3 py-1 text-xs text-blue-400 transition-colors duration-150 hover:bg-blue-500/30',
            onClick: () => handleReview(rowData)
          }, '审核')
        : h('span', {
            class: 'rounded-full bg-green-500/20 px-2 py-1 text-xs font-medium text-green-400'
          }, '已审核');
    }
  }
];
// ==================== 方法定义 ====================
const handleReview = (rowData: RobotStatusData) => {
  // console.log("🚀 ~ handleReview ~ rowData:", rowData);
  reviewForm.value = {
    id: rowData.id,
    module: rowData.module,
    function: rowData.function,
    warning: WARNING_MAP[rowData.warning],
    error: rowData.error,
    robot: rowData.robot,
    approval: ""
  };
  reviewVisible.value = true;
};

const handleReviewCancel = () => {
  reviewVisible.value = false;
  reviewForm.value = _.clone(initialReviewForm);
};

const handleReviewConfirm = () => {
  message.success("审核成功");
  reviewVisible.value = false;
  reviewForm.value = _.clone(initialReviewForm);
};
// ==================== 生命周期 ====================
onMounted(() => {
  getTableList();
});
</script>


