<template>
  <div
    class="hover:shadow-3xl flex h-full flex-col rounded-2xl border border-slate-700/50 bg-slate-800/60 p-4 shadow-2xl backdrop-blur-sm transition-all duration-300 hover:border-slate-600/50 hover:bg-slate-800/80"
  >
    <!-- 标题栏 -->
    <div class="mb-4 flex items-center justify-between border-b border-slate-700/50 pb-3">
      <div class="flex items-center gap-3">
        <div class="h-2 w-2 animate-pulse rounded-full bg-green-500"></div>
        <Terminal class="h-5 w-5 text-green-400" />
        <h3 class="text-lg font-semibold text-slate-200">控制台日志</h3>
        <div class="ml-2 flex items-center gap-2">
          <div class="rounded border border-green-500/30 bg-green-500/20 px-2 py-1 text-xs font-medium text-green-400">实时</div>
          <div class="text-xs text-slate-400">{{ consoleLogList.length }}/20</div>
        </div>
      </div>
      <div class="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          @click="clearLogs"
          class="p-2 text-slate-400 hover:bg-slate-700/50 hover:text-slate-200"
        >
          <Trash2 class="h-4 w-4" />
        </Button>
      </div>
    </div>

    <!-- 日志内容区域 -->
    <div class="min-h-0 flex-1 overflow-hidden rounded-lg border border-slate-700/30 bg-slate-900/50">
      <ScrollArea class="h-full">
        <div class="space-y-2 p-3">
          <div
            v-for="item in consoleLogList"
            :key="item.id"
            class="group rounded-lg border border-slate-700/30 bg-slate-800/30 p-3 transition-all duration-200 hover:border-slate-600/50 hover:bg-slate-800/50"
          >
            <!-- 日志头部 -->
            <div class="mb-2 flex items-center gap-2">
              <div class="flex items-center gap-2">
                <component :is="getLogTypeIconComponent(item.type)" :class="getLogTypeIcon(item.type)" />
                <span :class="getLogTypeColor(item.type)" class="rounded border px-2 py-1 text-xs font-medium">
                  {{ item.type }}
                </span>
              </div>
              <div class="font-mono text-xs text-slate-400">
                {{ item.time }}
              </div>
            </div>

            <!-- 日志内容 -->
            <div class="font-mono text-sm leading-relaxed break-all text-slate-300">
              {{ item.cont }}
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="consoleLogList.length === 0" class="flex flex-col items-center justify-center py-8 text-slate-500">
            <Terminal class="mb-2 h-8 w-8 opacity-50" />
            <span class="text-sm">暂无日志信息</span>
          </div>
        </div>
      </ScrollArea>
    </div>
  </div>
</template>

<script setup lang="ts">
// ==================== 导入依赖 ====================
import { ref, onMounted, onUnmounted } from "vue";
import { Terminal, Trash2, Info, AlertTriangle, XCircle, CheckCircle } from "lucide-vue-next";
import dayjs from "dayjs";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";

// ==================== 页面组件导入 ====================

// ==================== 全局组件导入 ====================

// ==================== 其他导入项 ====================
import { generateUUID } from "@/utils";
// ==================== TypeScript ====================
interface Props {}

type ConsoleLogItem = {
  id: string;
  type: string;
  time: string;
  cont: string;
};
// ==================== vue宏定义 ====================
withDefaults(defineProps<Props>(), {});
// ==================== 钩子定义 ====================

// ==================== 状态定义 ====================
const timer = ref<number | null>(null);
const consoleLogList = ref<ConsoleLogItem[]>([]);
// ==================== 方法定义 ====================
const setConsoleLogList = () => {
  if (consoleLogList.value.length > 20) {
    timer.value && clearInterval(timer.value);
    return;
  }

  const logTypes = ["INFO", "WARN", "ERROR", "SUCCESS"];
  const messages = [
    "前往位置点x:-0.17,y:0.03.",
    "系统初始化完成",
    "连接服务器成功",
    "数据同步中...",
    "任务执行完成",
    "检测到异常状态",
    "正在重新连接...",
    "配置文件已更新"
  ];

  consoleLogList.value.unshift({
    id: generateUUID(),
    type: logTypes[Math.floor(Math.random() * logTypes.length)],
    time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
    cont: messages[Math.floor(Math.random() * messages.length)]
  });
};

// 清空日志
const clearLogs = () => {
  consoleLogList.value = [];
};

// 获取日志类型颜色
const getLogTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    INFO: "text-blue-400 bg-blue-500/20 border-blue-500/30",
    WARN: "text-yellow-400 bg-yellow-500/20 border-yellow-500/30",
    ERROR: "text-red-400 bg-red-500/20 border-red-500/30",
    SUCCESS: "text-green-400 bg-green-500/20 border-green-500/30"
  };
  return colorMap[type] || "text-slate-400 bg-slate-500/20 border-slate-500/30";
};

// 获取日志类型图标组件
const getLogTypeIconComponent = (type: string) => {
  const iconMap: Record<string, any> = {
    INFO: Info,
    WARN: AlertTriangle,
    ERROR: XCircle,
    SUCCESS: CheckCircle
  };
  return iconMap[type] || Info;
};

// 获取日志类型图标样式
const getLogTypeIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    INFO: "w-3 h-3 text-blue-400",
    WARN: "w-3 h-3 text-yellow-400",
    ERROR: "w-3 h-3 text-red-400",
    SUCCESS: "w-3 h-3 text-green-400"
  };
  return iconMap[type] || "w-3 h-3 text-slate-400";
};
// ==================== 生命周期 ====================
onMounted(() => {
  timer.value = window.setInterval(setConsoleLogList, 2000);
});

onUnmounted(() => {
  timer.value && clearInterval(timer.value);
});
</script>
