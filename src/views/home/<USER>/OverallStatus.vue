<template>
  <div class="rounded-2xl border border-slate-700/50 bg-slate-800/60 shadow-2xl backdrop-blur-sm transition-all duration-300 hover:border-slate-600/50 hover:bg-slate-800/80 hover:shadow-3xl p-4 h-full flex flex-col">
    <!-- 标题栏 -->
    <div class="flex items-center justify-between border-b border-slate-700/50 bg-slate-700/30 p-3 mb-4 rounded-lg">
      <div class="flex items-center space-x-3">
        <div class="h-2 w-2 animate-pulse rounded-full bg-green-400"></div>
        <BotIcon class="h-5 w-5 text-slate-300" />
        <span class="text-sm font-medium text-slate-200">系统总状态</span>
      </div>
      <div class="text-xs text-slate-400">
        {{ statusList.length }} 项监控
      </div>
    </div>

    <!-- 状态列表 -->
    <div class="flex-1 overflow-hidden">
      <ul class="grid grid-cols-2 gap-3 h-full content-start">
      <li
        v-for="item in statusList"
        :key="item.id"
        class="group flex items-center justify-between rounded-xl border border-slate-600/30 bg-slate-700/40 p-4 transition-all duration-200 hover:border-slate-500/50 hover:bg-slate-700/60 hover:shadow-lg"
      >
        <div class="flex items-center space-x-3">
          <div class="relative">
            <component
              :is="item.icon"
              :class="clsx(
                'h-5 w-5 transition-colors duration-200',
                {
                  'animate-pulse text-red-400': item.status === 5,
                  'animate-pulse text-orange-400': item.status === 3,
                  'text-green-400': item.status === 0,
                  'text-yellow-400': item.status === 1 || item.status === 4,
                  'text-slate-500': item.status === 2
                }
              )"
            />
            <!-- 状态指示点 -->
            <div
              v-if="item.status === 5 || item.status === 3"
              class="absolute -top-1 -right-1 h-2 w-2 rounded-full animate-ping"
              :class="{
                'bg-red-400': item.status === 5,
                'bg-orange-400': item.status === 3
              }"
            ></div>
          </div>
          <div class="flex-1">
            <p class="text-sm font-medium text-slate-200 group-hover:text-slate-100 transition-colors duration-200">
              {{ item.label }}
            </p>
            <p
              :class="clsx(
                'text-xs font-medium transition-colors duration-200',
                {
                  'text-green-400': item.status === 0,
                  'text-yellow-400': item.status === 1 || item.status === 4,
                  'text-slate-500': item.status === 2,
                  'text-orange-400': item.status === 3,
                  'text-red-400': item.status === 5
                }
              )"
            >
              {{ STATUS_TEXT_MAP[item.status] }}
            </p>
          </div>
        </div>

        <!-- 右侧状态指示器 -->
        <div class="flex items-center space-x-2">
          <div
            class="h-2 w-2 rounded-full transition-all duration-200"
            :class="{
              'bg-green-400 shadow-lg shadow-green-400/50': item.status === 0,
              'bg-yellow-400 shadow-lg shadow-yellow-400/50': item.status === 1 || item.status === 4,
              'bg-slate-500': item.status === 2,
              'bg-orange-400 shadow-lg shadow-orange-400/50 animate-pulse': item.status === 3,
              'bg-red-400 shadow-lg shadow-red-400/50 animate-pulse': item.status === 5
            }"
          ></div>
        </div>
      </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
// ==================== 导入依赖 ====================
import { onMounted, ref } from "vue";
import type { FunctionalComponent } from "vue";
import clsx from "clsx";
import {
  BotIcon,
  TrafficConeIcon,
  BatteryIcon,
  ThermometerIcon,
  BatteryFullIcon,
  BoxIcon,
  SignalIcon,
  RadioTowerIcon,
  Navigation2Icon,
  HousePlugIcon
} from "lucide-vue-next";
// ==================== 页面组件导入 ====================

// ==================== 全局组件导入 ====================

// ==================== 其他导入项 ====================
import { overallStatusApi } from "@/api/modules/mock";
// ==================== TypeScript ====================
/** 0 正常  1 一般  2 无数据  3 断开  4 一般  5 危急警告 */
type RobotStatus = 0 | 1 | 2 | 3 | 4 | 5;

type RawStatusItem = {
  id: number;
  label: string;
  status: RobotStatus;
  icon: string; // 字符串
};

type StatusItem = Omit<RawStatusItem, "icon"> & {
  icon: FunctionalComponent; // Vue 组件类型
};
// ==================== vue宏定义 ====================

// ==================== 钩子定义 ====================

// ==================== 状态定义 ====================
const ICON_MAP = {
  BotIcon,
  TrafficConeIcon,
  BatteryIcon,
  ThermometerIcon,
  BatteryFullIcon,
  BoxIcon,
  SignalIcon,
  RadioTowerIcon,
  Navigation2Icon,
  HousePlugIcon
} as const;

const STATUS_TEXT_MAP: Record<RobotStatus, string> = {
  0: "正常",
  1: "一般告警",
  2: "无数据",
  3: "断开",
  4: "一般",
  5: "危急警告"
};



const statusList = ref<StatusItem[]>([]);
// ==================== 方法定义 ====================
const getStatusList = async () => {
  const res = await overallStatusApi();
  // console.log(res);
  statusList.value = (res.data as RawStatusItem[]).map(item => {
    return {
      ...item,
      icon: ICON_MAP[item.icon as keyof typeof ICON_MAP]
    };
  });
};
// ==================== 生命周期 ====================
onMounted(() => {
  getStatusList();
});
</script>
