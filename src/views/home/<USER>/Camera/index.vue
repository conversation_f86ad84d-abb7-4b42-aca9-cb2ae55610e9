<template>
  <div
    v-bind="attrs"
    :class="
      clsx(
        'hover:shadow-3xl flex h-75 flex-col overflow-hidden rounded-2xl border border-slate-700/50 bg-slate-800/60 shadow-2xl backdrop-blur-sm transition-all duration-300 hover:border-slate-600/50 hover:bg-slate-800/80'
      )
    "
  >
    <!-- 标题栏 -->
    <div class="flex items-center justify-between border-b border-slate-700/50 bg-slate-700/30 p-4">
      <div class="flex items-center space-x-3">
        <div class="h-2 w-2 animate-pulse rounded-full bg-green-400"></div>
        <cctv-icon class="h-5 w-5 text-slate-300" />
        <span class="text-sm font-medium text-slate-200">{{ title }}</span>
      </div>
      <div class="flex items-center space-x-2">
        <!-- 录制状态指示 -->
        <button
          class="flex items-center space-x-2 rounded-lg px-2 py-1 transition-colors duration-200 hover:bg-red-500/30"
          :class="isRecording ? 'bg-red-500/20' : 'bg-slate-600/20'"
          @click="toggleRecording"
        >
          <div class="h-2 w-2 rounded-full" :class="isRecording ? 'animate-pulse bg-red-400' : 'bg-slate-400'"></div>
          <span class="text-xs" :class="isRecording ? 'text-red-300' : 'text-slate-400'">
            {{ isRecording ? "REC" : "STOP" }}
          </span>
        </button>
        <!-- 控制按钮 -->
        <button
          class="group rounded-lg bg-slate-600/50 p-2 transition-colors duration-200 hover:bg-slate-500/50"
          @click="handleOpenControlPanel"
        >
          <sliders-vertical-icon class="h-4 w-4 text-slate-300 group-hover:text-slate-100" />
        </button>
      </div>
    </div>

    <!-- 视频显示区域 -->
    <div class="relative flex-1 overflow-hidden bg-slate-900/50">
      <Player :src="url" />

      <!-- 视频覆盖层信息 -->
      <div class="absolute top-3 left-3 flex flex-col space-y-2">
        <!-- 时间戳 -->
        <div class="rounded bg-black/60 px-2 py-1 font-mono text-xs text-white backdrop-blur-sm">
          {{ currentTime }}
        </div>
      </div>

      <!-- 底部控制条 -->
      <div v-if="false" class="absolute right-0 bottom-0 left-0 bg-gradient-to-t from-black/80 to-transparent p-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <!-- 播放控制 -->
            <button
              class="rounded-full bg-white/20 p-1.5 transition-colors hover:bg-white/30"
              @click="handlePlay"
              :disabled="isPlaying"
              :class="{ 'opacity-50': isPlaying }"
            >
              <play-icon class="h-4 w-4 text-white" />
            </button>
            <button
              class="rounded-full bg-white/20 p-1.5 transition-colors hover:bg-white/30"
              @click="handlePause"
              :disabled="!isPlaying"
              :class="{ 'opacity-50': !isPlaying }"
            >
              <pause-icon class="h-4 w-4 text-white" />
            </button>
          </div>

          <div class="flex items-center space-x-3">
            <!-- 全屏按钮 -->
            <button class="rounded-full bg-white/20 p-1.5 transition-colors hover:bg-white/30" @click="handleFullscreen">
              <maximize-icon class="h-4 w-4 text-white" />
            </button>
            <!-- 截图按钮 -->
            <button class="rounded-full bg-white/20 p-1.5 transition-colors hover:bg-white/30" @click="handleScreenshot">
              <camera-icon class="h-4 w-4 text-white" />
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <control-panel ref="controlPanel" />
</template>

<script setup lang="ts">
import { useAttrs, useTemplateRef, ref, onMounted, onUnmounted } from "vue";
import { CctvIcon, SlidersVerticalIcon, PlayIcon, PauseIcon, MaximizeIcon, CameraIcon } from "lucide-vue-next";
import clsx from "clsx";
import dayjs from "dayjs";
import Player from "@/components/Player/index.vue";
import ControlPanel from "./ControlPanel.vue";

interface Props {
  title: string;
  url: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: "",
  url: ""
});

const attrs = useAttrs();
const controlPanelRef = useTemplateRef("controlPanel");
const currentTime = ref("");

// 视频播放状态
const isPlaying = ref(false);
const isRecording = ref(true); // 录制状态

// 更新时间显示
const updateTime = () => {
  currentTime.value = dayjs().format("HH:mm:ss");
};

let timeInterval: NodeJS.Timeout;

onMounted(() => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});

// ==================== 事件处理函数 ====================

/**
 * 打开控制面板
 */
const handleOpenControlPanel = () => {
  controlPanelRef.value?.open();
};

/**
 * 播放视频
 */
const handlePlay = () => {
  console.log(`[${props.title}] 开始播放视频`);
  isPlaying.value = true;
  // TODO: 实现视频播放逻辑
};

/**
 * 暂停视频
 */
const handlePause = () => {
  console.log(`[${props.title}] 暂停播放视频`);
  isPlaying.value = false;
  // TODO: 实现视频暂停逻辑
};

/**
 * 全屏显示
 */
const handleFullscreen = () => {
  console.log(`[${props.title}] 进入全屏模式`);
  // TODO: 实现全屏显示逻辑
  // 可以使用 Fullscreen API 或者触发父组件的全屏事件
};

/**
 * 截图功能
 */
const handleScreenshot = () => {
  console.log(`[${props.title}] 截图保存`);
  // TODO: 实现截图功能
  // 可以使用 canvas 来截取当前视频帧

  // 模拟截图成功提示
  // 这里可以集成消息提示组件
  // alert(`${props.title} 截图已保存`);
};

/**
 * 切换录制状态
 */
const toggleRecording = () => {
  isRecording.value = !isRecording.value;
  console.log(`[${props.title}] ${isRecording.value ? "开始" : "停止"}录制`);
  // TODO: 实现录制功能
};
</script>

<style scoped></style>
