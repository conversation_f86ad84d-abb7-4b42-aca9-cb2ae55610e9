<template>
  <Sheet v-model:open="visible">
    <SheetContent
      side="bottom"
      class="mx-auto mb-2 h-[480px] w-[1040px] rounded-2xl overflow-hidden border border-slate-700/50 bg-slate-800/90 backdrop-blur-sm"
      :auto-focus="false"
      :closeable="false"
      @open-auto-focus="e => e.preventDefault()"
    >
      <SheetHeader class="border-b border-slate-700/50 bg-slate-700/30 p-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="h-2 w-2 animate-pulse rounded-full bg-blue-400"></div>
            <CctvIcon class="h-5 w-5 text-slate-300" />
            <SheetTitle class="text-sm font-medium text-slate-200">操控中心</SheetTitle>
          </div>
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-3">
              <span class="text-sm text-slate-300">焦距（B-G）：</span>
              <NumberField :model-value="globalStore.robotStatus.cameraMultiple" :min="0" :max="100" class="w-24">
                <NumberFieldContent>
                  <NumberFieldDecrement class="text-white" />
                  <NumberFieldInput
                    placeholder="请输入"
                    class="border-slate-500/50 bg-slate-600/50 text-slate-200"
                    @focus="inputFocus"
                    @blur="inputBlur"
                  />
                  <NumberFieldIncrement class="text-white" />
                </NumberFieldContent>
              </NumberField>
              <button
                class="group rounded-lg bg-slate-600/50 px-3 py-1.5 text-xs text-slate-300 transition-colors duration-200 hover:bg-slate-500/50 hover:text-slate-100"
                @click="handleClearScreen"
              >
                清屏
              </button>
            </div>
            <div class="flex items-center space-x-2">
              <Select v-model="selectedFeature">
                <SelectTrigger class="w-32 border-slate-600/50 bg-slate-600/50 text-slate-300 hover:bg-slate-500/50">
                  <SelectValue placeholder="功能选择" />
                </SelectTrigger>
                <SelectContent class="border-slate-700 bg-slate-800">
                  <SelectItem
                    v-for="option in state.featureOptions"
                    :key="option.value"
                    :value="option.value"
                    class="text-slate-300 hover:bg-slate-700"
                  >
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
              <button
                class="group rounded-lg bg-slate-600/50 p-2 transition-colors duration-200 hover:bg-slate-500/50"
                @click="visible = false"
              >
                <XIcon class="h-4 w-4 text-slate-300 group-hover:text-slate-100" />
              </button>
            </div>
          </div>
        </div>
      </SheetHeader>

      <div class="flex h-75 items-center justify-between bg-slate-800/40 px-20 py-6">
        <!-- 左侧控制面板 -->
        <div class="space-y-6">
          <!-- 雨刮和补光灯控制 -->
          <div class="flex items-center gap-x-4">
            <Button
              variant="outline"
              class="group flex items-center space-x-2 border-slate-600/50 bg-slate-700/50 text-slate-300 transition-all duration-150 hover:bg-slate-600/50 hover:text-slate-100 active:scale-95"
              @click="handleWiperOn"
            >
              <span>开雨刮{{ globalStore.robotStatus.wiper }}</span>
              <span class="h-2 w-2 rounded-full bg-blue-400"></span>
            </Button>
            <Button
              variant="outline"
              class="group flex items-center space-x-2 border-slate-600/50 bg-slate-700/50 text-slate-300 transition-all duration-150 hover:bg-slate-600/50 hover:text-slate-100 active:scale-95"
              @click="handleWiperOff"
            >
              <span>关雨刮</span>
              <span class="h-2 w-2 rounded-full bg-blue-400"></span>
            </Button>
            <Button
              variant="outline"
              class="group flex items-center space-x-2 border-slate-600/50 bg-slate-700/50 text-slate-300 transition-all duration-150 hover:bg-slate-600/50 hover:text-slate-100 active:scale-95"
              @click="handleLightOn"
            >
              <span>开补光灯</span>
              <span class="h-2 w-2 rounded-full bg-blue-400"></span>
            </Button>
            <Button
              variant="outline"
              class="group flex items-center space-x-2 border-slate-600/50 bg-slate-700/50 text-slate-300 transition-all duration-150 hover:bg-slate-600/50 hover:text-slate-100 active:scale-95"
              @click="handleLightOff"
            >
              <span>关补光灯</span>
              <span class="h-2 w-2 rounded-full bg-blue-400"></span>
            </Button>
          </div>

          <!-- 水平和垂直控制 -->
          <div
            class="flex items-center justify-center space-x-10 rounded-xl border border-slate-600/50 bg-slate-700/30 px-6 py-3 shadow-sm"
          >
            <div class="flex items-center space-x-2">
              <span class="text-slate-300">水平</span>
              <NumberField :model-value="globalStore.robotStatus.ptzHor" :min="-180" :max="180" class="w-24">
                <NumberFieldContent>
                  <NumberFieldDecrement class="text-white" />
                  <NumberFieldInput
                    class="border-slate-500/50 bg-slate-600/50 text-center text-slate-200"
                    @focus="inputFocus"
                    @blur="inputBlur"
                  />
                  <NumberFieldIncrement class="text-white" />
                </NumberFieldContent>
              </NumberField>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-slate-300">垂直</span>
              <NumberField :model-value="globalStore.robotStatus.ptzVer" :min="-90" :max="90" class="w-24">
                <NumberFieldContent>
                  <NumberFieldDecrement class="text-white" />
                  <NumberFieldInput
                    class="border-slate-500/50 bg-slate-600/50 text-center text-slate-200"
                    @focus="inputFocus"
                    @blur="inputBlur"
                  />
                  <NumberFieldIncrement class="text-white" />
                </NumberFieldContent>
              </NumberField>
            </div>
          </div>

          <!-- 其他功能按钮 -->
          <div class="flex items-center gap-x-4">
            <Button
              variant="outline"
              class="group flex items-center space-x-2 border-slate-600/50 bg-slate-700/50 text-slate-300 transition-all duration-150 hover:bg-slate-600/50 hover:text-slate-100 active:scale-95"
              @click="handleReset"
            >
              <span>复位</span>
              <span class="h-2 w-2 rounded-full bg-blue-400"></span>
            </Button>
            <Button
              variant="outline"
              class="group flex items-center space-x-2 border-slate-600/50 bg-slate-700/50 text-slate-300 transition-all duration-150 hover:bg-slate-600/50 hover:text-slate-100 active:scale-95"
              @click="handleStartRecording"
            >
              <span>开始录像</span>
              <span class="h-2 w-2 rounded-full bg-blue-400"></span>
            </Button>
            <Button
              variant="outline"
              class="group flex items-center space-x-2 border-slate-600/50 bg-slate-700/50 text-slate-300 transition-all duration-150 hover:bg-slate-600/50 hover:text-slate-100 active:scale-95"
              @click="handleStopRecording"
            >
              <span>结束录像</span>
              <span class="h-2 w-2 rounded-full bg-blue-400"></span>
            </Button>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger as-child>
                  <Button
                    variant="ghost"
                    size="icon"
                    class="rounded-full bg-slate-700/50 text-slate-300 transition-all duration-150 hover:scale-110 hover:bg-slate-600/50 hover:text-slate-100 active:scale-95"
                  >
                    <ChevronLeftIcon class="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>聚焦-前调</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger as-child>
                  <Button
                    variant="ghost"
                    size="icon"
                    class="rounded-full bg-slate-700/50 text-slate-300 transition-all duration-150 hover:scale-110 hover:bg-slate-600/50 hover:text-slate-100 active:scale-95"
                  >
                    <ChevronRightIcon class="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>聚焦-后调</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        <!-- 中间方向控制区 -->
        <div class="grid grid-cols-3 grid-rows-3 gap-2">
          <!-- W -->
          <Button
            ref="directionWButtonRef"
            class="direction-btn col-start-2 row-start-1 h-12 w-12 bg-green-500 text-lg font-semibold text-white transition-all duration-150 hover:bg-green-600 active:scale-95 active:bg-green-700"
            @click="handleDirectionW"
          >
            <div class="flex flex-col items-center">
              <ChevronUpIcon class="h-4 w-4" />
              <span class="text-xs">W</span>
            </div>
          </Button>
          <Button
            ref="directionAButtonRef"
            class="direction-btn col-start-1 row-start-2 h-12 w-12 bg-green-500 text-lg font-semibold text-white transition-all duration-150 hover:bg-green-600 active:scale-95 active:bg-green-700"
            @click="handleDirectionA"
          >
            <div class="flex flex-col items-center">
              <ChevronLeftIcon class="h-4 w-4" />
              <span class="text-xs">A</span>
            </div>
          </Button>
          <Button
            ref="directionDButtonRef"
            class="direction-btn col-start-3 row-start-2 h-12 w-12 bg-green-500 text-lg font-semibold text-white transition-all duration-150 hover:bg-green-600 active:scale-95 active:bg-green-700"
            @click="handleDirectionD"
          >
            <div class="flex flex-col items-center">
              <span class="text-xs">D</span>
              <ChevronRightIcon class="h-4 w-4" />
            </div>
          </Button>
          <Button
            ref="directionSButtonRef"
            class="direction-btn col-start-2 row-start-3 h-12 w-12 bg-green-500 text-lg font-semibold text-white transition-all duration-150 hover:bg-green-600 active:scale-95 active:bg-green-700"
            @click="handleDirectionS"
          >
            <div class="flex flex-col items-center">
              <ChevronDownIcon class="h-4 w-4" />
              <span class="text-xs">S</span>
            </div>
          </Button>
        </div>

        <!-- 右侧控制面板 -->
        <div class="flex flex-col items-center gap-y-4">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger as-child>
                <Button
                  variant="ghost"
                  size="icon"
                  class="rounded-full bg-slate-700/50 hover:bg-slate-600/50 text-slate-300 hover:text-slate-100 transition-all duration-150 hover:scale-110 active:scale-95"
                  @click="handleZoomIn"
                >
                  <PlusIcon class="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>放大</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <div class="flex flex-col items-center space-y-2">
            <Slider
              v-model="zoomValue"
              orientation="vertical"
              class="h-24 [&_[data-orientation=vertical]]:bg-slate-600/50 [&_[role=slider]]:bg-slate-300 [&_[role=slider]]:border-slate-400 [&_[data-orientation=vertical]_[data-slider-range]]:bg-blue-400"
              :max="100"
              :min="0"
              :step="1"
            />
            <div class="rounded bg-slate-700/50 px-2 py-1 font-mono text-xs text-slate-300 border border-slate-600/50">
              {{ zoomValue[0] }}%
            </div>
          </div>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger as-child>
                <Button
                  variant="ghost"
                  size="icon"
                  class="rounded-full bg-slate-700/50 hover:bg-slate-600/50 text-slate-300 hover:text-slate-100 transition-all duration-150 hover:scale-110 active:scale-95"
                  @click="handleZoomOut"
                >
                  <MinusIcon class="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>缩小</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <!-- 底部控制区 -->
      <SheetFooter class="border-t border-slate-700/50 bg-slate-800/20 pt-4">
        <div class="flex w-full items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
              <span class="text-slate-300">车体速度</span>
              <NumberField :model-value="globalStore.robotStatus.speed" :min="0" :max="100" class="w-24">
                <NumberFieldContent>
                  <NumberFieldDecrement class="text-white" />
                  <NumberFieldInput
                    class="border-slate-500/50 bg-slate-600/50 text-slate-200"
                    @focus="inputFocus"
                    @blur="inputBlur"
                  />
                  <NumberFieldIncrement class="text-white" />
                </NumberFieldContent>
              </NumberField>
            </div>
            <div>
              <RadioGroup v-model="runningMode" class="flex space-x-4">
                <div v-for="r in state.runModeOptions" :key="r.value" class="flex items-center space-x-2">
                  <RadioGroupItem
                    :value="r.value"
                    :id="r.value"
                    class="w-4 h-4"
                  />
                  <Label
                    :for="r.value"
                    class="text-sm cursor-pointer transition-colors"
                    :class="runningMode === r.value ? 'text-blue-400 font-medium' : 'text-slate-300'"
                  >
                    {{ r.label }}
                  </Label>
                </div>
              </RadioGroup>
            </div>
          </div>
        </div>
      </SheetFooter>
    </SheetContent>
  </Sheet>
</template>

<script setup lang="ts">
// ==================== 导入依赖 ====================
import { reactive, ref, useTemplateRef, onUnmounted, watch } from "vue";
import {
  CctvIcon,
  XIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  PlusIcon,
  MinusIcon
} from "lucide-vue-next";

// ==================== shadcn 组件导入 ====================
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetFooter } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import {
  NumberField,
  NumberFieldContent,
  NumberFieldDecrement,
  NumberFieldIncrement,
  NumberFieldInput
} from "@/components/ui/number-field";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// ==================== 其他导入项 ====================
import { useGlobalStore } from "@/store/modules/global";
// ==================== TypeScript ====================
type RunModelValue = "driving" | "stationary" | "parking";

interface FeatureOption {
  label: string;
  value: string;
}

interface State {
  featureOptions: FeatureOption[];
  runModeOptions: { label: string; value: RunModelValue }[];
}

// ==================== 钩子定义 ====================
const globalStore = useGlobalStore();

// ==================== 组件实例 ====================
const directionWButtonRef = useTemplateRef("directionWButtonRef");
const directionAButtonRef = useTemplateRef("directionAButtonRef");
const directionDButtonRef = useTemplateRef("directionDButtonRef");
const directionSButtonRef = useTemplateRef("directionSButtonRef");

// ==================== 状态定义 ====================
const state = reactive<State>({
  featureOptions: [
    { label: "自动巡检", value: "auto" },
    { label: "手动控制", value: "manual" },
    { label: "紧急停止", value: "emergency" }
  ],
  runModeOptions: [
    { label: "行车模式", value: "driving" },
    { label: "原地模式", value: "stationary" },
    { label: "停车模式", value: "parking" }
  ]
});

const visible = ref(false);
const runningMode = ref<RunModelValue>("driving");
const selectedFeature = ref<string>("");
const zoomValue = ref([50]); // Slider 组件需要数组格式
// ==================== 方法定义 ====================
const open = () => {
  visible.value = true;
};

// ==================== 按钮事件处理函数 ====================

/**
 * 清屏功能
 */
const handleClearScreen = () => {
  console.log("清屏操作");
  // TODO: 实现清屏逻辑
};

/**
 * 开启雨刮器
 */
const handleWiperOn = () => {
  console.log("开启雨刮器");
  // TODO: 实现雨刮器开启逻辑
};

/**
 * 关闭雨刮器
 */
const handleWiperOff = () => {
  console.log("关闭雨刮器");
  // TODO: 实现雨刮器关闭逻辑
};

/**
 * 开启补光灯
 */
const handleLightOn = () => {
  console.log("开启补光灯");
  // TODO: 实现补光灯开启逻辑
};

/**
 * 关闭补光灯
 */
const handleLightOff = () => {
  console.log("关闭补光灯");
  // TODO: 实现补光灯关闭逻辑
};

/**
 * 复位操作
 */
const handleReset = () => {
  console.log("执行复位操作");
  // TODO: 实现复位逻辑
};

/**
 * 开始录像
 */
const handleStartRecording = () => {
  console.log("开始录像");
  // TODO: 实现开始录像逻辑
};

/**
 * 停止录像
 */
const handleStopRecording = () => {
  console.log("停止录像");
  // TODO: 实现停止录像逻辑
};

/**
 * 方向控制 - 向前 (W)
 */
const handleDirectionW = () => {
  console.log("方向控制: 向前 (W)");
  // TODO: 实现向前移动逻辑
};

/**
 * 方向控制 - 向左 (A)
 */
const handleDirectionA = () => {
  console.log("方向控制: 向左 (A)");
  // TODO: 实现向左移动逻辑
};

/**
 * 方向控制 - 向右 (D)
 */
const handleDirectionD = () => {
  console.log("方向控制: 向右 (D)");
  // TODO: 实现向右移动逻辑
};

/**
 * 方向控制 - 向后 (S)
 */
const handleDirectionS = () => {
  console.log("方向控制: 向后 (S)");
  // TODO: 实现向后移动逻辑
};

/**
 * 放大操作
 */
const handleZoomIn = () => {
  console.log("放大操作");
  if (zoomValue.value[0] < 100) {
    zoomValue.value = [Math.min(100, zoomValue.value[0] + 10)];
  }
  // TODO: 实现放大逻辑
};

/**
 * 缩小操作
 */
const handleZoomOut = () => {
  console.log("缩小操作");
  if (zoomValue.value[0] > 0) {
    zoomValue.value = [Math.max(0, zoomValue.value[0] - 10)];
  }
  // TODO: 实现缩小逻辑
};

const KeyboardControls = (e: KeyboardEvent) => {
  // 防止重复触发
  if (e.repeat) return;

  switch (e.key.toLowerCase()) {
    case "w":
      simulateButtonPress(directionWButtonRef.value?.$el);
      handleDirectionW();
      break;
    case "a":
      simulateButtonPress(directionAButtonRef.value?.$el);
      handleDirectionA();
      break;
    case "d":
      simulateButtonPress(directionDButtonRef.value?.$el);
      handleDirectionD();
      break;
    case "s":
      simulateButtonPress(directionSButtonRef.value?.$el);
      handleDirectionS();
      break;
  }
};

/**
 * 模拟按钮按下的视觉效果
 */
const simulateButtonPress = (buttonElement: HTMLElement | undefined) => {
  if (!buttonElement) return;

  // 添加键盘按下效果的类
  buttonElement.classList.add("keyboard-pressed");

  // 200ms 后移除效果
  setTimeout(() => {
    buttonElement.classList.remove("keyboard-pressed");
  }, 200);
};

const inputFocus = () => {
  document.removeEventListener("keydown", KeyboardControls);
};

const inputBlur = () => {
  document.addEventListener("keydown", KeyboardControls);
};

// ==================== 生命周期 ====================
// 监听 visible 变化来管理键盘事件
watch(visible, newValue => {
  if (newValue) {
    // Sheet 打开时添加键盘监听
    document.addEventListener("keydown", KeyboardControls);
  } else {
    // Sheet 关闭时移除键盘监听
    document.removeEventListener("keydown", KeyboardControls);
  }
});

onUnmounted(() => {
  document.removeEventListener("keydown", KeyboardControls);
});

defineExpose({
  open
});
</script>

<style scoped>
/* 隐藏 Sheet 默认的关闭按钮 */
:deep([data-sheet-close]) {
  display: none !important;
}

:deep(.sheet-close) {
  display: none !important;
}

/* 隐藏可能的关闭图标 */
:deep([aria-label="Close"]) {
  display: none !important;
}

/* 隐藏 shadcn sheet 的默认关闭按钮 */
:deep(.absolute.right-4.top-4) {
  display: none !important;
}

/* 方向按钮的键盘按下效果 */
.direction-btn {
  position: relative;
}

.direction-btn::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  background: rgba(255, 255, 255, 0.2);
  opacity: 0;
  transition: opacity 0.15s ease;
  pointer-events: none;
}

.direction-btn.keyboard-pressed::after {
  opacity: 1;
}

/* 增强按钮的过渡效果 */
.direction-btn {
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.direction-btn:active,
.direction-btn.keyboard-pressed {
  transform: scale(0.95);
  background-color: rgb(21 128 61); /* bg-green-700 */
}

/* Slider 深色主题样式 */
:deep([data-orientation="vertical"]) {
  background-color: rgb(71 85 105 / 0.5) !important; /* slate-600/50 */
}

:deep([data-slider-track]) {
  background-color: rgb(71 85 105 / 0.5) !important; /* slate-600/50 */
}

:deep([data-slider-range]) {
  background-color: rgb(96 165 250) !important; /* blue-400 */
}

:deep([role="slider"]) {
  background-color: rgb(203 213 225) !important; /* slate-300 */
  border-color: rgb(148 163 184) !important; /* slate-400 */
}

:deep([role="slider"]:hover) {
  background-color: rgb(226 232 240) !important; /* slate-200 */
}

:deep([role="slider"]:focus) {
  box-shadow: 0 0 0 2px rgb(96 165 250 / 0.5) !important; /* blue-400/50 */
}
</style>

<style scoped></style>
