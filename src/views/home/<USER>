export interface RobotRunningStatus {
  /** 当前电量百分比 */
  batteryPercent: number;
  /** 电池温度 */
  batteryTemp: number;
  /** 相机倍数 */
  cameraMultiple: number;
  /** 充电电流 */
  chargeCurr: number;
  /** 充电电压 */
  chargeVoltage: number;
  /** 机身温度 */
  devTemp: number;
  /** 湿度（环境） */
  humidity: number;
  /** 是否遇到障碍物 */
  isObstacle: string;
  /** 车灯状态（开/关） */
  light: string;
  /** 机器人行驶里程 */
  mileage: number;
  /** 
   * 控制模式（参考枚举说明表：机器人实时状态-控制模式（5））
   *    "任务模式：501
        后台遥控：502
        紧急定位：503
        手持遥控：504"
   */
  mode: string;
  /**
   * 导航通信状态（参考枚举说明表：机器人实时状态-在线状态（3））
   *    "在线 301
        离线 302"
   */
  naviStatus: string;
  /** 是否遇到障碍物 */
  obstacle: string;
  /** 所在站点地图的朝向 */
  orientation: string;
  /** 云台通信状态（参考枚举说明表：机器人实时状态-在线状态（3）） */
  panStatus: string;
  /** 当前所在位置纬度 */
  posLat: string;
  /** 当前所在位置经度 */
  posLng: string;
  /** 所在站点地图的坐标X */
  posX: string;
  /** 所在站点地图的坐标Y */
  posY: string;
  /** 所在站点地图的坐标Z */
  posZ: string;
  /** 云台水平角度 */
  ptzHor: number;
  /** 云台垂直角度 */
  ptzVer: number;
  /** 表计识别状态（参考枚举说明表：机器人实时状态-在线状态（3）） */
  regnStatus: string;
  /** 机器人唯一标记 */
  robotSn: string;
  /**
   * 机器人运行状态（参考枚举说明表：机器人实时状态-运行状态（7））
   * 空闲、巡检、充电、故障、离线、返航
   */
  runStatus: string;
  /** 机器人运行时间 */
  runtime: number;
  /** 机器人运行速度 */
  speed: number;
  /** 站点ID */
  substation: string;
  /** 温度（环境） */
  temperature: number;
  /** 风速（环境） */
  windspeed: number;
  /** 雨刷状态（开/关） */
  wiper: string;
}
