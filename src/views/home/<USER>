<template>
  <n-config-provider class="h-full" :theme-overrides="themeOverrides">
    <Toaster rich-colors />
    <main class="flex h-full flex-col bg-[#222222] text-white">
      <!-- 页面头部 -->
      <Header @open-menu="openMenu" />
      <!-- 主要内容区域 - 三栏布局 -->
      <div class="over scrollable grid flex-1 grid-cols-[25rem_1fr_25rem] items-start gap-x-4 p-4">
        <!-- 左侧区域：摄像头组件 + 总状态 -->
        <div class="h-full space-y-4">
          <!-- 可见光影视区 -->
          <Camera title="可见光影视区" :url="visibleLightUrl" />
          <!-- 红外线影视区 -->
          <Camera title="红外线影视区" :url="infraredUrl" />
          <!-- 总状态组件 -->
          <div class="flex-1">
            <overall-status />
          </div>
        </div>
        <!-- 中间区域：可切换组件 + 统计数据 -->
        <div class="flex h-full flex-col space-y-4">
          <!-- 实时定位 -->
          <div class="h-120 flex-shrink-0">
            <RealTimeLocation
              title="实时定位"
              :pos-x="robotPose.posX"
              :pos-y="robotPose.posY"
              :orientation="robotPose.orientation"
              :robot-icon="'robot'"
            />
          </div>

          <!-- 统计数据组件 -->
          <div class="h-157 min-h-0">
            <statistical-data />
          </div>
        </div>
        <!-- 右侧区域：信息展示组件 -->
        <div class="flex h-full flex-col space-y-4">
          <!-- 巡检信息 -->
          <div class="flex-shrink-0">
            <inspection-info />
          </div>
          <!-- 任务信息 -->
          <div class="h-100 min-h-0">
            <task-info />
          </div>
          <!-- 控制台日志 -->
          <div class="h-93 min-h-0">
            <console-log />
          </div>
        </div>
      </div>
    </main>
    <SideMenu ref="sideMenuRef" />
  </n-config-provider>
</template>

<script setup lang="ts">
// ==================== 导入依赖 ====================
import { ref, useTemplateRef, onUnmounted } from "vue";
import { NConfigProvider, type GlobalThemeOverrides } from "naive-ui";
import { Toaster } from "vue-sonner";
import "vue-sonner/style.css";
import { useWebSocket } from "@vueuse/core";

import Header from "./components/Header.vue";
import OverallStatus from "./components/OverallStatus.vue";
import StatisticalData from "./components/StatisticalData/index.vue";
import InspectionInfo from "./components/InspectionInfo.vue";
import TaskInfo from "./components/TaskInfo.vue";
import ConsoleLog from "./components/ConsoleLog.vue";
import Camera from "./components/Camera/index.vue";
import RealTimeLocation from "./components/RealTimeLocation.vue";
import SideMenu from "./components/SideMenu.vue";

import { useGlobalStore } from "@/store/modules/global";
import { useAuthStore } from "@/store/modules/auth";
import { useWsDataStore } from "@/store/modules/wsData";
import type { RobotRunningStatus } from "./interface";

import { cameraDeviceListApi } from "@/api/modules/robot";
import type { CameraDeviceResult } from "@/api/interface/robot";
// ==================== 类型定义 ====================
const sideMenu = useTemplateRef("sideMenuRef");
const globalStore = useGlobalStore();
const authStore = useAuthStore();
const wsDataStore = useWsDataStore();
// ==================== 响应式数据 ====================
const robotPose = ref({ posX: "0", posY: "0", orientation: "0" }); // 添加测试数据

const cameraList = ref<CameraDeviceResult[]>([]);
const visibleLightUrl = ref("");
const infraredUrl = ref("");
// ==================== 工具函数 ====================
const { close } = useWebSocket<string>(`ws://192.168.1.53:18086/websocket/${authStore.role.id}`, {
  // heartbeat: { message: JSON.stringify({ ping: "ping" }) }
  heartbeat: false,
  onMessage: (_ws, event) => {
    const wsData: WsDataType = JSON.parse(event.data);
    if (wsData.topic === "xxx/robot/event/robotRunningStatus") {
      const result: RobotRunningStatus = JSON.parse(wsData.data);
      console.log(result);
      // 天气数据
      wsDataStore.setRobotWeatherData({
        temperature: result.temperature,
        humidity: result.humidity,
        windspeed: result.windspeed
      });
      wsDataStore.setRobotRealtimeInfo({
        speed: result.speed,
        orientation: result.orientation,
        posX: result.posX,
        posY: result.posY,
        ptzHor: result.ptzHor,
        ptzVer: result.ptzVer,
        batteryPercent: result.batteryPercent,
        chargeVoltage: result.chargeVoltage,
        chargeCurr: result.chargeCurr
      });
      robotPose.value = {
        posX: result.posX,
        posY: result.posY,
        orientation: result.orientation
      };
    }
  }
});
// ==================== 业务逻辑函数 ====================
const getCameraList = async () => {
  const res = await cameraDeviceListApi();
  cameraList.value = res.data.records;
  const visibleLight = cameraList.value.find(v => v.id === Number(import.meta.env.VITE_VISIBLE_LIGHT));
  visibleLightUrl.value = visibleLight?.flvUrl || "";
  visibleLightUrl.value = "https://sf1-cdn-tos.huoshanstatic.com/obj/media-fe/xgplayer_doc_video/flv/xgplayer-demo-720p.flv";

  const infrared = cameraList.value.find(v => v.id === Number(import.meta.env.VITE_INFRARED));
  infraredUrl.value = infrared?.flvUrl || "";
};
getCameraList();
// 打开菜单的方法
const openMenu = () => {
  sideMenu.value?.open();
};

globalStore.getRobotStatus();

onUnmounted(() => {
  close();
});
// ==================== 主题配置 ====================
/**
 * Naive UI 主题覆盖配置
 * 适配深色主题的组件样式
 */
const themeOverrides: GlobalThemeOverrides = {
  // 标签页样式
  Tabs: {
    tabTextColorBar: "#fff",
    tabTextColorHoverBar: "var(--color-primary-2)",
    tabTextColorActiveBar: "var(--color-primary-2)",
    barColor: "var(--color-primary-2)"
  },

  // 开关样式
  Switch: {
    buttonColor: "#fff",
    railColorActive: "var(--color-primary-2)",
    railColor: "#E2E8F0"
  },

  // 数据表格样式
  DataTable: {
    thColor: "var(--color-primary-1)",
    thTextColor: "#fff",
    tdColor: "transparent",
    tdTextColor: "#fff",
    tdColorHover: "transparent",
    borderColor: "transparent"
  },
  // 分页器样式
  Pagination: {
    buttonColor: "transparent",
    buttonBorder: "none",
    buttonBorderHover: "none",
    itemTextColorHover: "var(--color-primary-1)",
    itemTextColor: "#fff",
    itemColorActive: "transparent",
    itemBorderActive: "1px solid var(--color-primary-1)",
    itemTextColorActive: "var(--color-primary-1)",
    itemBorderDisabled: "none",
    itemTextColorDisabled: "var(--color-primary-1)",
    itemColorDisabled: "transparent",
    buttonBorderPressed: "transparent",
    buttonIconColor: "var(--color-primary-1)",
    buttonIconColorHover: "var(--color-primary-1)",
    jumperTextColor: "#fff"
  },

  // 按钮样式
  Button: {
    textColor: "#fff",
    border: "1px solid var(--color-primary-1)",
    borderHover: "1px solid var(--color-primary-1)",
    textColorHover: "var(--color-primary-2)",
    textColorPressed: "var(--color-primary-2)",
    textColorFocus: "var(--color-primary-2)",
    borderPressed: "1px solid var(--color-primary-1)",
    borderFocus: "var(--color-primary-1)"
  },

  // 单选框样式
  Radio: {
    textColor: "#fff"
  },
  Card: {
    borderColor: "var(border-primary-1)",
    colorModal: "hsla(219, 75%, 46%, 0.9)",
    titleTextColor: "#fff",
    closeIconColor: "#fff",
    closeIconColorHover: "#fff"
  }
};
</script>
