<template>
  <Sheet v-model:open="visible">
    <SheetContent
      side="right"
      class="w-80 border-slate-700/50 bg-slate-900/95 p-0 backdrop-blur-sm [&>button]:border [&>button]:border-slate-600/50 [&>button]:bg-slate-700/50 [&>button]:text-slate-300 [&>button]:hover:bg-slate-600/50 [&>button]:hover:text-slate-100"
    >
      <!-- 菜单头部 -->
      <SheetHeader class="border-b border-slate-700/50 bg-slate-800/50 p-6">
        <div class="flex items-center gap-3">
          <div class="h-2 w-2 animate-pulse rounded-full bg-blue-500"></div>
          <Menu class="h-5 w-5 text-blue-400" />
          <SheetTitle class="text-lg font-semibold text-slate-200">导航菜单</SheetTitle>
        </div>
      </SheetHeader>

      <!-- 菜单内容 -->
      <div class="flex-1 overflow-hidden">
        <ScrollArea class="h-full">
          <div class="space-y-2 p-4 pb-6">
            <!-- 首页菜单项 -->
            <RouterLink
              to="/home"
              @click="visible = false"
              :class="[
                'flex cursor-pointer items-center gap-3 rounded-lg px-4 py-3 transition-all duration-200',
                route.path === '/home'
                  ? 'border border-blue-500/30 bg-blue-500/20 text-blue-400'
                  : 'text-slate-300 hover:bg-slate-800/50 hover:text-slate-200'
              ]"
            >
              <Home class="h-4 w-4" />
              <span class="font-medium">首页</span>
              <div v-if="route.path === '/home'" class="ml-auto h-2 w-2 rounded-full bg-blue-500"></div>
            </RouterLink>

            <!-- 动态菜单项 -->
            <template v-for="item in menuOptions" :key="item.key">
              <!-- 有子菜单的项 -->
              <div v-if="item.children" class="space-y-1">
                <div
                  @click="toggleSubmenu(item.key as string)"
                  class="flex cursor-pointer items-center gap-3 rounded-lg px-4 py-3 text-slate-300 transition-all duration-200 hover:bg-slate-800/50 hover:text-slate-200"
                >
                  <component :is="getMenuIcon(item.key as string)" class="h-4 w-4" />
                  <span class="font-medium">{{ item.label }}</span>
                  <ChevronDown
                    class="ml-auto h-4 w-4 transition-transform duration-200"
                    :class="{ 'rotate-180': expandedMenus.includes(item.key as string) }"
                  />
                </div>

                <!-- 子菜单 -->
                <div v-show="expandedMenus.includes(item.key as string)" class="ml-6 space-y-1 border-l border-slate-700/50 pl-4">
                  <RouterLink
                    v-for="child in item.children"
                    :key="child.key"
                    :to="child.key as string"
                    @click="visible = false"
                    :class="[
                      'flex cursor-pointer items-center gap-3 rounded-md px-3 py-2 transition-all duration-200',
                      route.path === child.key
                        ? 'border border-blue-500/30 bg-blue-500/20 text-blue-400'
                        : 'text-slate-400 hover:bg-slate-800/30 hover:text-slate-300'
                    ]"
                  >
                    <div class="h-2 w-2 rounded-full bg-slate-500"></div>
                    <span class="text-sm">{{ child.label }}</span>
                    <div v-if="route.path === child.key" class="ml-auto h-1.5 w-1.5 rounded-full bg-blue-500"></div>
                  </RouterLink>
                </div>
              </div>

              <!-- 无子菜单的项 -->
              <RouterLink
                v-else
                :to="item.key as string"
                @click="visible = false"
                :class="[
                  'flex cursor-pointer items-center gap-3 rounded-lg px-4 py-3 transition-all duration-200',
                  route.path === item.key
                    ? 'border border-blue-500/30 bg-blue-500/20 text-blue-400'
                    : 'text-slate-300 hover:bg-slate-800/50 hover:text-slate-200'
                ]"
              >
                <component :is="getMenuIcon(item.key as string)" class="h-4 w-4" />
                <span class="font-medium">{{ item.label }}</span>
                <div v-if="route.path === item.key" class="ml-auto h-2 w-2 rounded-full bg-blue-500"></div>
              </RouterLink>
            </template>
          </div>
        </ScrollArea>
      </div>
    </SheetContent>
  </Sheet>
</template>

<script setup lang="ts">
// ==================== 导入依赖 ====================
import { ref } from "vue";
import { useRoute, RouterLink } from "vue-router";
import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Menu, Home, ChevronDown, Settings, Users, FileText, BarChart3, Shield, Database, Wrench } from "lucide-vue-next";

// ==================== 页面组件导入 ====================

// ==================== 全局组件导入 ====================

// ==================== 其他导入项 ====================
import { useAuthStore } from "@/store/modules/auth";

// ==================== 钩子定义 ====================
const authStore = useAuthStore();
const route = useRoute();

// ==================== 类型定义 ====================
interface MenuOption {
  key: string;
  label: string;
  children?: MenuOption[];
}

// ==================== 状态定义 ====================
const visible = ref(false);
const menuOptions = ref<MenuOption[]>([]);
const expandedMenus = ref<string[]>([]);
// ==================== 方法定义 ====================
const open = () => {
  visible.value = true;
  menuOptions.value = getMenuOptions(authStore.showMenuListGet);
};

const getMenuOptions = (menuList: Menu.MenuOptions[]): MenuOption[] => {
  const newMenuList: Menu.MenuOptions[] = JSON.parse(JSON.stringify(menuList));

  return newMenuList.map(item => {
    return {
      key: item.path,
      label: item.meta.title,
      children: item.children ? getMenuOptions(item.children) : undefined
    };
  });
};

// 切换子菜单展开状态
const toggleSubmenu = (key: string) => {
  const index = expandedMenus.value.indexOf(key);
  if (index > -1) {
    expandedMenus.value.splice(index, 1);
  } else {
    expandedMenus.value.push(key);
  }
};

// 获取菜单图标
const getMenuIcon = (key: string) => {
  const iconMap: Record<string, any> = {
    "/taskManagement": FileText,
    "/systemManagement": Settings,
    "/userManagement": Users,
    "/dataAnalysis": BarChart3,
    "/security": Shield,
    "/database": Database,
    "/tools": Wrench
  };
  return iconMap[key] || FileText;
};
// ==================== 生命周期 ====================

defineExpose({
  open,
  visible
});
</script>

<style scoped></style>
