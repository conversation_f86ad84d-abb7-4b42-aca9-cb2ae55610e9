<template>
  <div
    class="hover:shadow-3xl flex h-full flex-col rounded-2xl border border-slate-700/50 bg-slate-800/60 p-4 shadow-2xl backdrop-blur-sm transition-all duration-300 hover:border-slate-600/50 hover:bg-slate-800/80"
  >
    <!-- 标题栏 -->
    <div class="mb-4 flex items-center justify-between border-b border-slate-700/50 pb-3">
      <div class="flex items-center gap-3">
        <div class="h-2 w-2 rounded-full bg-blue-500"></div>
        <BookText class="h-5 w-5 text-blue-400" />
        <h3 class="text-lg font-semibold text-slate-200">任务信息</h3>
      </div>
    </div>

    <!-- 统计数据行 -->
    <div class="mb-2 flex items-center justify-between px-2">
      <div v-for="item in taskInfo" :key="item.id" class="text-center">
        <div class="text-sm font-bold" :class="getStatColor(item.color)">
          {{ item.value }}
        </div>
        <div class="text-xs text-slate-400">
          {{ item.title }}
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="min-h-0 flex-1 overflow-hidden rounded-lg border border-slate-700/30 bg-slate-800/50">
      <DataTable
        :columns="dataTableColumns"
        :data="tableData"
        :loading="loading"
        :pagination="{
          total: pageable.total,
          limit: pageable.limit,
          page: pageable.page
        }"
        :display-order="['pages']"
        @page-change="handleCurrentChange"
        @page-size-change="handleSizeChange"
        class="h-full"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
// ==================== 导入依赖 ====================
import { onMounted } from "vue";
import { BookText } from "lucide-vue-next";
import DataTable from "@/components/common/DataTable.vue";

// ==================== 页面组件导入 ====================

// ==================== 全局组件导入 ====================

// ==================== 其他导入项 ====================
import { overallTaskInfoListApi, overallTaskInfoApi } from "@/api/modules/mock";

import { useTable, useList } from "@/hooks";
// ==================== TypeScript ====================
interface Props {}

type TaskInfoType = { id: number; title: string; value: string; color: string };
// ==================== vue宏定义 ====================
withDefaults(defineProps<Props>(), {});
// ==================== 钩子定义 ====================
const { getTableList, loading, tableData, pageable, handleCurrentChange, handleSizeChange } = useTable(overallTaskInfoListApi);
const [taskInfo, getTaskInfo] = useList<TaskInfoType>(overallTaskInfoApi);
// ==================== 状态定义 ====================
const dataTableColumns = [
  {
    key: "name",
    title: "名称",
    width: "auto",
    ellipsis: true
  },
  {
    key: "speed",
    title: "速度",
    width: "auto",
    ellipsis: true
  },
  {
    key: "status",
    title: "状态",
    width: "auto",
    ellipsis: true
  }
];

// ==================== 方法定义 ====================
const getStatColor = (color: string) => {
  const colorMap: Record<string, string> = {
    "text-primary-2": "text-blue-400",
    "text-primary-3": "text-green-400",
    "text-primary-4": "text-purple-400",
    "text-primary-5": "text-orange-400"
  };
  return colorMap[color] || "text-slate-300";
};

// ==================== 生命周期 ====================
onMounted(() => {
  getTableList();
  getTaskInfo({}, { loading: false });
});
</script>
