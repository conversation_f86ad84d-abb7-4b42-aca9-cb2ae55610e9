<template>
  <div
    class="hover:shadow-3xl flex h-full flex-col rounded-2xl border border-slate-700/50 bg-slate-800/60 p-3 shadow-2xl backdrop-blur-sm transition-all duration-300 hover:border-slate-600/50 hover:bg-slate-800/80"
  >
    <!-- 标题栏 -->
    <div class="mb-3 flex items-center gap-2 border-b border-slate-700/50 pb-2">
      <div class="h-2 w-2 rounded-full bg-blue-500"></div>
      <Search class="h-4 w-4 text-blue-400" />
      <h3 class="text-lg font-semibold text-slate-200">巡检信息</h3>
    </div>

    <!-- 统计数据区域 -->
    <div class="mb-3 flex items-start justify-between px-2">
      <!-- 巡检统计 -->
      <div class="flex-1">
        <div class="mb-2 flex items-center gap-1">
          <ListCheck class="h-4 w-4 text-green-400" />
          <span class="text-sm text-slate-200">巡检</span>
        </div>
        <div class="mb-1 flex justify-between text-sm text-slate-400">
          <span>月:</span>
          <span class="text-base font-bold text-green-400">
            <n-number-animation :from="0" :to="inspectionInfo.inspection.month" />
          </span>
        </div>
        <div class="flex justify-between text-sm text-slate-400">
          <span>日:</span>
          <span class="text-base font-bold text-green-300">
            <n-number-animation :from="0" :to="inspectionInfo.inspection.day" />
          </span>
        </div>
      </div>

      <!-- 分隔线 -->
      <div class="mx-4 h-16 w-px bg-slate-700/50"></div>

      <!-- 告警统计 -->
      <div class="flex-1">
        <div class="mb-2 flex items-center gap-1">
          <TriangleAlert class="h-4 w-4 text-red-400" />
          <span class="text-sm text-slate-200">告警</span>
        </div>
        <div class="mb-1 flex justify-between text-sm text-slate-400">
          <span>月:</span>
          <span class="text-base font-bold text-red-400">
            <n-number-animation :from="0" :to="inspectionInfo.alarm.month" />
          </span>
        </div>
        <div class="flex justify-between text-sm text-slate-400">
          <span>日:</span>
          <span class="text-base font-bold text-red-300">
            <n-number-animation :from="0" :to="inspectionInfo.alarm.day" />
          </span>
        </div>
      </div>
    </div>

    <!-- 机器人状态区域 -->
    <div class="flex-1 space-y-2">
      <!-- 机器人信息和运行模式合并 -->
      <div class="rounded border border-slate-700/30 bg-slate-800/50 p-2">
        <div class="mb-2 flex items-center justify-between">
          <div class="flex items-center gap-2">
            <Bot class="h-4 w-4 text-blue-400" />
            <span class="text-sm text-slate-200">机器人</span>
            <span class="rounded border border-blue-500/30 bg-blue-500/20 px-2 py-1 font-mono text-xs text-blue-400">
              N00001
            </span>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-sm text-slate-400">状态</span>
            <span class="rounded border border-green-500/30 bg-green-500/20 px-2 py-1 text-xs text-green-400"> 正常 </span>
          </div>
        </div>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <Play class="h-4 w-4 text-purple-400" />
            <span class="rounded border border-purple-500/30 bg-purple-500/20 px-2 py-1 text-xs text-purple-400"> 运行模式 </span>
          </div>
          <span class="text-sm text-slate-400">任务: 返航-进行</span>
        </div>
      </div>

      <!-- 控制模式选择 -->
      <div class="rounded border border-slate-700/30 bg-slate-800/50 p-2">
        <div class="mb-2 flex items-center gap-2">
          <Settings class="h-4 w-4 text-orange-400" />
          <span class="text-sm text-slate-200">控制模式</span>
        </div>
        <RadioGroup v-model="robotStatus" class="flex gap-4">
          <div
            v-for="item in robotStatusTabs"
            :key="item.value"
            class="flex items-center space-x-2"
            @click="handleClickControlMode(item)"
          >
            <RadioGroupItem :value="item.value" :id="`mode-${item.value}`" class="h-4 w-4" />
            <Label
              :for="`mode-${item.value}`"
              class="cursor-pointer text-sm whitespace-nowrap transition-colors"
              :class="robotStatus === item.value ? 'font-medium text-blue-400' : 'text-slate-300'"
            >
              {{ item.label }}
            </Label>
          </div>
        </RadioGroup>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// ==================== 导入依赖 ====================
import { onMounted, reactive, ref } from "vue";
import { Search, ListCheck, TriangleAlert, Bot, Play, Settings } from "lucide-vue-next";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { toast } from "vue-sonner";

// ==================== 页面组件导入 ====================

// ==================== 全局组件导入 ====================

// ==================== 其他导入项 ====================
import { overallInspectionInfoApi } from "@/api/modules/mock";
// ==================== TypeScript ====================
type InspectionInfo = {
  inspection: { month: number; day: number };
  alarm: { month: number; day: number };
};

type RobotStatusTabsItem = {
  label: string;
  value: "1" | "2" | "3";
};
// ==================== vue宏定义 ====================

// ==================== 钩子定义 ====================

// ==================== 状态定义 ====================
const inspectionInfo = ref<InspectionInfo>({ inspection: { month: 0, day: 0 }, alarm: { month: 0, day: 0 } });
const robotStatus = ref("");

const robotStatusTabs = reactive<RobotStatusTabsItem[]>([
  {
    label: "巡检",
    value: "1"
  },
  {
    label: "挂起/手控",
    value: "2"
  },
  {
    label: "返航",
    value: "3"
  }
]);
// ==================== 方法定义 ====================
const getInspectionInfo = async () => {
  const res = await overallInspectionInfoApi();
  inspectionInfo.value = res.data as InspectionInfo;
};

const handleClickControlMode = (item: (typeof robotStatusTabs)[number]) => {
  if (item.value === "1") {
    return;
  }

  switch (item.value) {
    case "2":
      toast.success("切换到巡检任务挂起模式", { position: "top-center" });
      break;
    case "3":
      toast.success("切换到返航模式", { position: "top-center" });
      break;
  }
};
// ==================== 生命周期 ====================
onMounted(() => {
  getInspectionInfo();
});
</script>
