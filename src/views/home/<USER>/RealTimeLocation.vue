<template>
  <div
    :class="
      clsx(
        'hover:shadow-3xl flex h-full flex-col overflow-hidden rounded-2xl border border-slate-700/50 bg-slate-800/60 shadow-2xl backdrop-blur-sm transition-all duration-300 hover:border-slate-600/50 hover:bg-slate-800/80'
      )
    "
  >
    <!-- 标题栏 -->
    <div class="flex items-center justify-between border-b border-slate-700/50 bg-slate-700/30 p-4">
      <div class="flex items-center space-x-3">
        <div :class="['h-2 w-2 rounded-full', isConnected ? 'animate-pulse bg-green-400' : 'bg-red-400']"></div>
        <locate-icon class="h-5 w-5 text-slate-300" />
        <span class="text-sm font-medium text-slate-200">实时位置</span>
        <span class="text-xs text-slate-400">
          {{ isConnected ? "已连接" : "未连接" }}
        </span>
        <span v-if="props.posX && props.posY" class="text-xs text-blue-400">
          位置: ({{ parseFloat(props.posX).toFixed(2) }}, {{ parseFloat(props.posY).toFixed(2) }})
        </span>
        <span v-if="props.orientation" class="text-xs text-green-400">
          朝向: {{ parseFloat(props.orientation).toFixed(1) }}°
        </span>
      </div>
      <div class="flex items-center space-x-2">
        <slot name="operation"></slot>
        <tooltip-provider>
          <!-- 放大按钮 -->
          <tooltip>
            <tooltip-trigger as-child>
              <button
                class="group rounded-lg bg-blue-600/50 p-2 transition-colors duration-200 hover:bg-blue-500/50"
                @click="zoomIn"
                title="放大地图"
              >
                <svg
                  class="h-4 w-4 text-slate-300 group-hover:text-slate-100"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </button>
            </tooltip-trigger>
            <tooltip-content>
              <span>放大地图</span>
            </tooltip-content>
          </tooltip>

          <!-- 缩小按钮 -->
          <tooltip>
            <tooltip-trigger as-child>
              <button
                class="group rounded-lg bg-orange-600/50 p-2 transition-colors duration-200 hover:bg-orange-500/50"
                @click="zoomOut"
                title="缩小地图"
              >
                <svg
                  class="h-4 w-4 text-slate-300 group-hover:text-slate-100"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 12H6" />
                </svg>
              </button>
            </tooltip-trigger>
            <tooltip-content>
              <span>缩小地图</span>
            </tooltip-content>
          </tooltip>

          <!-- 轨迹显示切换按钮 -->
          <tooltip>
            <tooltip-trigger>
              <button
                class="group rounded-lg p-2 transition-colors duration-200"
                :class="showTrajectory ? 'bg-green-600/50 hover:bg-green-500/50' : 'bg-gray-600/50 hover:bg-gray-500/50'"
                @click="toggleTrajectory"
                title="显示/隐藏轨迹"
              >
                <svg
                  class="h-4 w-4 text-slate-300 group-hover:text-slate-100"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
                  />
                </svg>
              </button>
            </tooltip-trigger>
            <tooltip-content>
              <span>显示/隐藏轨迹</span>
            </tooltip-content>
          </tooltip>

          <!-- 清除轨迹按钮 -->
          <tooltip>
            <tooltip-trigger as-child>
              <button
                class="group rounded-lg bg-red-600/50 p-2 transition-colors duration-200 hover:bg-red-500/50"
                @click="clearTrajectory"
                title="清除轨迹"
              >
                <svg
                  class="h-4 w-4 text-slate-300 group-hover:text-slate-100"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                  />
                </svg>
              </button>
            </tooltip-trigger>
            <tooltip-content>
              <span>清除轨迹</span>
            </tooltip-content>
          </tooltip>

          <!-- 重置按钮 -->
          <tooltip>
            <tooltip-trigger as-child>
              <button
                class="group rounded-lg bg-slate-600/50 p-2 transition-colors duration-200 hover:bg-slate-500/50"
                @click="resetMapView"
                title="重置地图视图"
              >
                <svg
                  class="h-4 w-4 text-slate-300 group-hover:text-slate-100"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
              </button>
            </tooltip-trigger>
            <tooltip-content>
              <span>重置地图视图</span>
            </tooltip-content>
          </tooltip>
        </tooltip-provider>
      </div>
    </div>

    <!-- 地图内容区域 -->
    <div class="relative flex-1 overflow-hidden">
      <div
        id="ros-map"
        ref="ros-map"
        class="h-full w-full"
        style="touch-action: none; user-select: none"
        @mousedown="handleMouseDown"
        @mousemove="handleMouseMove"
        @mouseup="handleMouseUp"
        @mouseleave="handleMouseLeave"
        @wheel="handleWheel"
        @contextmenu.prevent
      ></div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="absolute inset-0 flex items-center justify-center bg-slate-800/50 backdrop-blur-sm">
        <div class="flex flex-col items-center space-y-2">
          <div class="h-8 w-8 animate-spin rounded-full border-2 border-blue-400 border-t-transparent"></div>
          <span class="text-sm text-slate-300">正在加载地图...</span>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-if="hasError" class="absolute inset-0 flex items-center justify-center bg-slate-800/50 backdrop-blur-sm">
        <div class="flex flex-col items-center space-y-2">
          <svg class="h-8 w-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
          <span class="text-sm text-red-300">地图加载失败</span>
          <button @click="reconnectRos" class="rounded bg-blue-600 px-3 py-1 text-xs text-white hover:bg-blue-700">
            重新连接
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, useTemplateRef, nextTick, watch } from "vue";
import { LocateIcon } from "lucide-vue-next";
import clsx from "clsx";
import { defaultRosConfig, type RosConfig } from "@/config/ros";
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip";

interface Props {
  posX: string;
  posY: string;
  orientation: string;
  robotIcon?: "robot" | "simple" | "car" | "mini" | "arrow"; // 机器人图标类型
}

const props = withDefaults(defineProps<Props>(), {
  posX: "",
  posY: "",
  orientation: "",
  robotIcon: "robot"
});

const emits = defineEmits<{
  fullScreen: [componentName: string, props: Props];
  exitFullScreen: [];
}>();

const rosMapRef = useTemplateRef("ros-map");

// ROS连接相关状态
const ros = ref<ROSLIB.Ros | null>(null);
const isConnected = ref(false);
const isLoading = ref(false);
const hasError = ref(false);

// 地图相关变量
let viewer: ROS2D.Viewer | null = null;
const gridClient = ref<any>(null);
const robotPoseClient = ref<any>(null);

// 机器人位置标记
let robotMarker: any = null;

// 缩放和平移控制
let zoomView: any = null;
let panView: any = null;

// 运动轨迹相关
let trajectoryPath: any = null;
const trajectoryPoints = ref<Array<{ x: number; y: number; timestamp: number }>>([]);
const maxTrajectoryPoints = ref(100); // 最大轨迹点数
const showTrajectory = ref(true); // 是否显示轨迹

// 鼠标交互相关状态
const isDragging = ref(false);
const lastMousePos = ref({ x: 0, y: 0 });
const mapScale = ref(1);

// ROS连接配置
// 使用配置文件中的ROS配置
const ROS_CONFIG: RosConfig = defaultRosConfig;

// 初始化ROS连接
const initRosConnection = async () => {
  try {
    isLoading.value = true;
    hasError.value = false;

    // 创建ROS连接
    ros.value = new ROSLIB.Ros({
      url: ROS_CONFIG.url
    });

    // 连接事件监听
    ros.value.on("connection", () => {
      console.log("ROS连接成功");
      isConnected.value = true;
      isLoading.value = false;
      hasError.value = false;
      initMapViewer();
    });

    ros.value.on("error", (error: any) => {
      console.error("ROS连接错误:", error);
      isConnected.value = false;
      isLoading.value = false;
      hasError.value = true;
    });

    ros.value.on("close", () => {
      console.log("ROS连接关闭");
      isConnected.value = false;
    });
  } catch (error) {
    console.error("初始化ROS连接失败:", error);
    isLoading.value = false;
    hasError.value = true;
  }
};

// 初始化地图查看器
const initMapViewer = async () => {
  await nextTick();

  if (!rosMapRef.value || !ros.value) return;

  try {
    // 获取容器尺寸
    const container = rosMapRef.value;
    const rect = container.getBoundingClientRect();

    // 创建地图查看器
    viewer = new ROS2D.Viewer({
      divID: "ros-map",
      width: rect.width || 800,
      height: rect.height || 600
    });

    // 创建缩放和平移控制
    zoomView = new ROS2D.ZoomView({
      rootObject: viewer.scene
    });

    panView = new ROS2D.PanView({
      rootObject: viewer.scene
    });

    // 订阅地图数据
    gridClient.value = new ROS2D.OccupancyGridClient({
      ros: ros.value,
      rootObject: viewer.scene,
      topic: ROS_CONFIG.topics.map
    });

    // 地图数据变化监听
    gridClient.value.on("change", handleMapChange);

    // 创建轨迹路径
    createTrajectoryPath();
  } catch (error) {
    console.error("初始化地图查看器失败:", error);
    hasError.value = true;
  }
};

// 处理地图数据变化
const handleMapChange = () => {
  if (!gridClient.value || !viewer) return;

  try {
    const grid = gridClient.value.currentGrid;
    if (grid) {
      // 自动缩放到地图尺寸
      viewer.scaleToDimensions(grid.width, grid.height);
      // 居中显示
      viewer.shift(grid.pose.position.x, grid.pose.position.y);
      mapScale.value = viewer.scaleX;
      console.log("地图数据更新完成");

      // 延迟创建机器人位置标记，确保地图完全渲染
      setTimeout(() => {
        createRobotMarker();
        // 更新机器人位置
        updateRobotPosition();
      }, 1000);
    }
  } catch (error) {
    console.error("处理地图变化失败:", error);
  }
};

// 创建机器人位置标记
const createRobotMarker = () => {
  console.log("开始创建机器人位置标记", {
    hasViewer: !!viewer,
    hasGridClient: !!gridClient.value
  });

  if (!viewer || !gridClient.value) {
    console.log("没有viewer或gridClient，无法创建标记");
    return;
  }

  try {
    // 如果标记已存在，先移除
    if (robotMarker && gridClient.value) {
      gridClient.value.rootObject.removeChild(robotMarker);
      robotMarker = null;
      console.log("移除旧的机器人标记");
    }

    // 根据当前图标类型选择图标
    let iconSrc = "/robot-car.svg";
    let iconSize = { width: 2, height: 2, regX: 1, regY: 1, scale: 0.5 }; // 缩小到50%

    // 使用SVG图标创建机器人标记
    const robotImage = new Image();
    robotImage.onload = () => {
      // 创建CreateJS的Bitmap对象
      robotMarker = new (window as any).createjs.Bitmap(robotImage);

      // 设置图片的注册点为中心
      robotMarker.regX = iconSize.regX;
      robotMarker.regY = iconSize.regY;

      // 设置缩放比例（使图标更小）
      robotMarker.scaleX = iconSize.scale;
      robotMarker.scaleY = iconSize.scale;

      // 添加到gridClient的rootObject
      gridClient.value.rootObject.addChild(robotMarker);

      // 设置初始位置
      robotMarker.x = 0;
      robotMarker.y = 0;
      robotMarker.rotation = 0;

      // 创建完成后立即更新位置
      updateRobotPosition();
    };

    // 使用选择的SVG图标
    robotImage.src = iconSrc;

    // 如果图片加载失败，使用备用方案
    robotImage.onerror = () => {
      console.log("SVG加载失败，使用NavigationArrow作为备用方案");
      // 使用原来的NavigationArrow作为备用
      robotMarker = new ROS2D.NavigationArrow({
        size: 0.5,
        strokeSize: 0.05,
        fillColor: "blue",
        pulse: true
      });
      gridClient.value.rootObject.addChild(robotMarker);
      robotMarker.x = 0;
      robotMarker.y = 0;
      robotMarker.rotation = 0;
      console.log("备用NavigationArrow标记创建成功");
      updateRobotPosition();
    };
  } catch (error) {
    console.error("创建机器人位置标记失败:", error);
  }
};

// 创建轨迹路径
const createTrajectoryPath = () => {
  if (!viewer) return;

  try {
    // 创建轨迹路径图形
    trajectoryPath = new (window as any).createjs.Shape();

    // 添加到场景
    viewer.scene.addChild(trajectoryPath);

    console.log("轨迹路径创建成功");
  } catch (error) {
    console.error("创建轨迹路径失败:", error);
  }
};

// 更新轨迹路径
const updateTrajectoryPath = () => {
  if (!trajectoryPath || !showTrajectory.value || trajectoryPoints.value.length < 2) return;

  try {
    // 清除之前的路径
    trajectoryPath.graphics.clear();

    // 设置路径样式
    trajectoryPath.graphics.setStrokeStyle(0.1).beginStroke("#00FF00"); // 绿色轨迹线

    // 绘制路径
    const points = trajectoryPoints.value;
    if (points.length > 0) {
      trajectoryPath.graphics.moveTo(points[0].x, points[0].y);

      for (let i = 1; i < points.length; i++) {
        trajectoryPath.graphics.lineTo(points[i].x, points[i].y);
      }
    }

    trajectoryPath.graphics.endStroke();
  } catch (error) {
    console.error("更新轨迹路径失败:", error);
  }
};

// 添加轨迹点
const addTrajectoryPoint = (x: number, y: number) => {
  const timestamp = Date.now();

  // 添加新点
  trajectoryPoints.value.push({ x, y, timestamp });

  // 限制轨迹点数量
  if (trajectoryPoints.value.length > maxTrajectoryPoints.value) {
    trajectoryPoints.value.shift(); // 移除最老的点
  }

  // 更新轨迹显示
  updateTrajectoryPath();
};

// 清除轨迹
const clearTrajectory = () => {
  trajectoryPoints.value = [];
  if (trajectoryPath) {
    trajectoryPath.graphics.clear();
  }
};

// 切换轨迹显示
const toggleTrajectory = () => {
  showTrajectory.value = !showTrajectory.value;

  if (trajectoryPath) {
    trajectoryPath.visible = showTrajectory.value;
  }

  // 如果显示轨迹，重新绘制
  if (showTrajectory.value) {
    updateTrajectoryPath();
  }
};

// 更新机器人位置显示（使用父组件传递的props）
const updateRobotPosition = () => {
  console.log("updateRobotPosition 被调用", {
    hasMarker: !!robotMarker,
    hasViewer: !!viewer,
    hasGrid: !!gridClient.value,
    posX: props.posX,
    posY: props.posY,
    orientation: props.orientation
  });

  if (!robotMarker || !viewer || !gridClient.value) {
    console.log("缺少必要组件，无法更新位置");
    return;
  }

  // 检查是否有有效的位置数据
  const posXNum = parseFloat(props.posX);
  const posYNum = parseFloat(props.posY);
  const orientationNum = parseFloat(props.orientation);

  console.log("解析后的位置数据:", { posXNum, posYNum, orientationNum });

  if (isNaN(posXNum) || isNaN(posYNum) || props.posX === "" || props.posY === "") {
    // 如果没有有效位置数据，隐藏标记
    console.log("位置数据无效，隐藏标记");
    robotMarker.visible = false;
    return;
  }

  try {
    const grid = gridClient.value.currentGrid;
    if (!grid) {
      console.log("没有地图网格数据");
      return;
    }

    console.log("地图网格信息:", {
      width: grid.width,
      height: grid.height,
      poseX: grid.pose.position.x,
      poseY: grid.pose.position.y
    });

    // 使用ROS坐标系统进行转换
    // 直接使用世界坐标，让ROS2D处理坐标转换
    robotMarker.x = posXNum;
    robotMarker.y = -posYNum;

    // 记录轨迹点（只有当位置发生变化时才记录）
    const lastPoint = trajectoryPoints.value[trajectoryPoints.value.length - 1];
    if (!lastPoint || Math.abs(lastPoint.x - posXNum) > 0.1 || Math.abs(lastPoint.y - -posYNum) > 0.1) {
      addTrajectoryPoint(posXNum, -posYNum);
    }

    // 设置机器人朝向
    if (!isNaN(orientationNum)) {
      robotMarker.rotation = orientationNum;
    }

    // 显示标记
    robotMarker.visible = true;
  } catch (error) {
    console.error("更新机器人位置失败:", error);
  }
};

// 鼠标事件处理
const handleMouseDown = (event: MouseEvent) => {
  isDragging.value = true;

  if (panView && rosMapRef.value) {
    const rect = rosMapRef.value.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    panView.startPan(x, y);
    rosMapRef.value.style.cursor = "grabbing";
  }

  lastMousePos.value = { x: event.clientX, y: event.clientY };
  event.preventDefault();
};

const handleMouseMove = (event: MouseEvent) => {
  if (!isDragging.value || !panView || !rosMapRef.value) return;

  const rect = rosMapRef.value.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;

  // 使用ROS2D的平移功能
  panView.pan(x, y);

  lastMousePos.value = { x: event.clientX, y: event.clientY };

  // 阻止默认行为，避免页面滚动
  event.preventDefault();
};

const handleMouseUp = () => {
  isDragging.value = false;
  if (rosMapRef.value) {
    rosMapRef.value.style.cursor = "grab";
  }
};

const handleMouseLeave = () => {
  isDragging.value = false;
  if (rosMapRef.value) {
    rosMapRef.value.style.cursor = "grab";
  }
};

const handleWheel = (event: WheelEvent) => {
  if (!zoomView || !rosMapRef.value) {
    return;
  }

  event.preventDefault();

  // 获取鼠标在地图上的位置
  const rect = rosMapRef.value.getBoundingClientRect();
  const mouseX = event.clientX - rect.left;
  const mouseY = event.clientY - rect.top;

  // 开始缩放
  zoomView.startZoom(mouseX, mouseY);

  // 计算缩放因子
  const scaleFactor = event.deltaY > 0 ? 0.9 : 1.1;

  // 应用缩放
  zoomView.zoom(scaleFactor);

  // 更新缩放比例记录
  if (viewer) {
    mapScale.value = viewer.scene.scaleX;
  }
};

// 放大地图
const zoomIn = () => {
  if (!zoomView || !viewer || !rosMapRef.value) return;

  const rect = rosMapRef.value.getBoundingClientRect();
  const centerX = rect.width / 2;
  const centerY = rect.height / 2;

  zoomView.startZoom(centerX, centerY);
  zoomView.zoom(1.2);

  mapScale.value = viewer.scene.scaleX;
};

// 缩小地图
const zoomOut = () => {
  if (!zoomView || !viewer || !rosMapRef.value) return;

  const rect = rosMapRef.value.getBoundingClientRect();
  const centerX = rect.width / 2;
  const centerY = rect.height / 2;

  zoomView.startZoom(centerX, centerY);
  zoomView.zoom(0.8);

  mapScale.value = viewer.scene.scaleX;
};

// 重置地图视图
const resetMapView = () => {
  if (!viewer || !gridClient.value) return;

  try {
    const grid = gridClient.value.currentGrid;
    if (grid) {
      viewer.scaleToDimensions(grid.width, grid.height);
      viewer.shift(grid.pose.position.x, grid.pose.position.y);
      mapScale.value = viewer.scaleX;
    }
  } catch (error) {
    console.error("重置地图视图失败:", error);
  }
};

// 重新连接ROS
const reconnectRos = () => {
  cleanupRos();
  initRosConnection();
};

// 清理ROS连接和资源
const cleanupRos = () => {
  try {
    // 清理事件监听
    if (gridClient.value) {
      gridClient.value.off("change", handleMapChange);
      gridClient.value = null;
    }

    if (robotPoseClient.value && typeof robotPoseClient.value.unsubscribe === "function") {
      robotPoseClient.value.unsubscribe();
      robotPoseClient.value = null;
    }

    // 清理机器人位置标记
    if (robotMarker && gridClient.value) {
      gridClient.value.rootObject.removeChild(robotMarker);
      robotMarker = null;
    }

    // 关闭ROS连接
    if (ros.value) {
      ros.value.close();
      ros.value = null;
    }

    // 清理viewer
    if (viewer) {
      viewer = null;
    }

    isConnected.value = false;
    isLoading.value = false;
    hasError.value = false;
  } catch (error) {
    console.error("清理ROS资源失败:", error);
  }
};

// 监听位置props变化，实时更新机器人位置
watch(
  () => [props.posX, props.posY, props.orientation],
  (newValues, oldValues) => {
    console.log("位置props发生变化:", { newValues, oldValues });
    updateRobotPosition();
  },
  { immediate: true } // 立即执行一次
);

// 组件生命周期
onMounted(() => {
  initRosConnection();
  if (rosMapRef.value) {
    rosMapRef.value.style.cursor = "grab";
  }
});

onUnmounted(() => {
  cleanupRos();
});

// 重置函数（供父组件调用）
const reset = () => {
  console.log("🚀 ~ reset ~ reset:", "实时位置");
  resetMapView();
};

defineExpose({
  reset,
  reconnectRos,
  resetMapView,
  clearTrajectory,
  toggleTrajectory
});
</script>
