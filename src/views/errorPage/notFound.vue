<template>
  <div class="flex h-full flex-col items-center justify-center space-y-4">
    <p class="text-base">404</p>
    <p class="text-5xl">找不到页面：{{ route.path }}</p>
    <Button class="mt-4" as-child>
      <Link :to="RoutePath.HOME">返回首页</Link>
    </Button>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";
import { Button } from "@/components/ui/button";
import Link from "@/components/Link/index.vue";

import { RoutePath } from "@/constants";

const route = useRoute();
</script>
