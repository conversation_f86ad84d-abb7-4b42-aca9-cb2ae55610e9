<!-- 设备信息总览 -->
<template>
  <div class="flex rounded-lg border border-primary-1 bg-[var(--card-visual-bg)] p-2">
    <div v-for="card in cards" :key="card.title" class="max-w-[170px] min-w-[130px] p-3">
      <!-- 标题 -->
      <h3 class="text-[15px] font-bold text-white">{{ card.title }}</h3>
      <!-- 分隔线 -->
      <div class="my-2 h-px bg-white opacity-40"></div>

      <!-- 内容 -->
      <ul class="space-y-1">
        <li v-for="item in card.items" :key="item.label" class="flex justify-between">
          <span class="text-[11px] text-primary-2">{{ item.label }}：</span>
          <span class="text-[11px] text-white">{{ item.value }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Item {
  label: string;
  value: string | number;
}

interface Card {
  title: string;
  items: Item[];
}

const cards: Card[] = [
  {
    title: "车体信息",
    items: [
      { label: "时间", value: "2025-06-09 10:46:40" },
      { label: "IP", value: "127.0.0.1" },
      { label: "速度", value: "0.0000331200" },
      { label: "方向", value: "90" },
      { label: "X轴", value: "0.24" },
      { label: "Y轴", value: "-2.60" }
    ]
  },
  {
    title: "云台信息",
    items: [
      { label: "水平转向", value: "315.30" },
      { label: "垂直转向", value: "-2.50" },
      { label: "可见光变焦", value: "1.00" },
      { label: "红外光聚焦", value: "-" }
    ]
  },
  {
    title: "充电房信息",
    items: [
      { label: "电量", value: "38.00" },
      { label: "电压", value: "71.00" },
      { label: "电流", value: "-2.00" },
      { label: "心跳时间", value: "-" },
      { label: "充电房门", value: "-" }
    ]
  }
];
</script>

<style scoped></style>
