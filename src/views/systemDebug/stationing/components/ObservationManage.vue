<!-- 观测点管理 -->
<template>
  <div class="flex flex-col space-y-2 rounded-lg border border-primary-1 bg-[var(--card-visual-bg)] p-2">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <div class="flex w-35 space-x-2">
          <span class="text-base">观测点管理</span>
        </div>
        <n-input-group>
          <n-input size="tiny" clearable :style="{ width: '50%' }" v-model:value="formValue.name" placeholder="输入名称" />
          <n-button size="tiny" type="info" ghost> 搜索 </n-button>
        </n-input-group>

        <ClipboardPlusIcon class="size-6" />
        <MaximizeIcon class="size-6" @click="clickMax" />
      </div>
    </div>
    <div class="flex-1">
      <n-data-table
        style="height: 10px; margin: 0; padding: 0"
        size="small"
        :bordered="false"
        :columns="columns"
        :data="data"
        flex-height
        :style="{ height: '100%' }"
        :pagination="{ page: 1 }"
      ></n-data-table>
    </div>
    <ObservationDialog ref="observationDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { h, ref } from "vue";
import { MaximizeIcon, ClipboardPlusIcon } from "lucide-vue-next";
import ObservationDialog from "./components/ObservationDialog.vue";
import { type DataTableColumn, type DataTableRowData, NButton } from "naive-ui";

const observationDialogRef = ref<InstanceType<typeof ObservationDialog>>();
const formValue = ref({
  name: ""
});

const clickMax = () => {
  observationDialogRef.value?.open();
};

const columns: DataTableColumn[] = [
  {
    key: "module",
    title: "设备名称",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },

  {
    key: "error",
    title: "原图",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: "action",
    title: "操作",
    width: "160",
    align: "center",
    render: () => {
      return [
        // "修改" 按钮
        h(
          NButton,
          {
            bordered: false,
            size: "tiny",
            focusable: false,
            style: {
              padding: "0", // 取消内边距
              paddingLeft: "8px", // 左侧内边距
              margin: "0" // 取消外边距
            }
          },
          { default: () => "修改" }
        ),
        // "删除" 按钮
        h(
          NButton,
          {
            bordered: false,
            size: "tiny",
            focusable: false,
            style: {
              padding: "0", // 取消内边距
              paddingLeft: "8px", // 左侧内边距
              margin: "0", // 取消外边距
              color: "#d74848" // 设置文本颜色为红色
            }
          },
          { default: () => "删除" }
        )
      ];
    }
  }
];

const data: DataTableRowData[] = Array(10).fill({
  module: "路线-R",
  error: ""
});
</script>

<style scoped>
:deep(.n-data-table .n-data-table-td) {
  padding: 1px;
  text-align: start;
  font-size: 11px;
  box-sizing: border-box;
  border: none;
  background-color: var(--n-merged-td-color);
  color: var(--n-td-text-color);
  border-bottom: 1px solid var(--n-merged-border-color);
  transition:
    box-shadow 0.3s var(--n-bezier),
    background-color 0.3s var(--n-bezier),
    border-color 0.3s var(--n-bezier),
    color 0.3s var(--n-bezier);
}
</style>
