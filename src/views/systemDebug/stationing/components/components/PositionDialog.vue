<!-- 位置点管理-列表 -->
<template>
  <n-modal v-model:show="showModal" preset="card" title="位置点管理" style="width: 60%">
    <Card>
      <CardContent class="flex h-full flex-col space-y-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-1">
            <span class="whitespace-nowrap">位置点管理：</span>
            <Input class="" v-model="searchParam.name" />
            <Button class="cursor-pointer" @click="search">搜索</Button>
          </div>
          <div class="space-x-2">
            <ButtonGroup>
              <Button @click="handleNewTaskInfo">新增 <PlusIcon class="ml-2 size-4" /></Button>
              <Button>修改 <PenLineIcon class="ml-2 size-4" /></Button>
              <Button variant="destructive">删除 <Trash2Icon class="ml-2 size-4" /></Button>
            </ButtonGroup>
          </div>
        </div>

        <n-data-table
          remote
          :data="tableData"
          :columns="columns"
          :row-key="(row: RowData) => row.id"
          striped
          flex-height
          style="height: 600px"
          :pagination="{
            showQuickJumper: true,
            showSizePicker: true,
            itemCount: pageable.total,
            displayOrder: ['pages', 'quick-jumper', 'size-picker'],
            suffix: info => {
              return '共' + info.itemCount + '条';
            },
            pageSize: pageable.limit,
            page: pageable.page,
            pageSizes: [10, 20, 50, 100],
            onChange: handleCurrentChange,
            onUpdatePageSize: handleSizeChange
          }"
        ></n-data-table>
      </CardContent>
    </Card>
  </n-modal>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { PlusIcon, PenLineIcon, Trash2Icon } from "lucide-vue-next";
import { type DataTableColumn, useMessage } from "naive-ui";
import { Card, CardContent } from "@/components/ui/card";
import { Button, ButtonGroup } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useTable } from "@/hooks";
import { getIntelligenceTaskListApi } from "@/api/modules/mock";

interface RowData {
  id: number;
  name: string;
  robot: string;
  validity: number;
  level: number;
  speed: number;
}

const { getTableList, searchParam, tableData, pageable, handleCurrentChange, handleSizeChange, search } =
  useTable(getIntelligenceTaskListApi);

const message = useMessage();
const showModal = ref(false);

const columns: DataTableColumn<RowData>[] = [
  {
    type: "selection"
  },
  {
    key: "name",
    title: "名称",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: "robot",
    title: "X坐标",
    align: "center"
  },
  {
    key: "level",
    title: "Y坐标",
    align: "center"
  },
  {
    key: "speed",
    title: "Z坐标",
    align: "center"
  }
];

const handleNewTaskInfo = () => {
  message.warning("请选择台账设备");
  //   newTaskInfoRef.value?.open();
};

const open = async () => {
  showModal.value = true;
  await getTableList();
};

onMounted(() => {});
defineExpose({
  open
});
</script>

<style scoped>
:deep(.n-data-table .n-data-table-th) {
  color: #444444 !important;
}
:deep(.n-data-table .n-data-table-td) {
  color: #606060;
}
:deep(.n-pagination .n-pagination-item) {
  color: #606060;
}
</style>
