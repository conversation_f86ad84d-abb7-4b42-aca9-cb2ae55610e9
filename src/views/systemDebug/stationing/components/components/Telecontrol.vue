<!-- 操控中心-弹框 -->
<template>
  <n-modal v-model:show="showModal" preset="card" title="操控中心" style="width: 60%">
    <div class="bg-opacity-80 space-y-4 rounded bg-cyan-500/10 p-4 text-orange-400">
      <!-- 顶部功能 -->
      <div class="flex items-center justify-between">
        <div class="flex text-lg font-bold">
          焦距 ( B <MoveRightIcon /> G )
          <n-input-number v-model:value="focalLength" button-placement="both" size="small" style="width: 110px" class="ml-2" />
        </div>

        <select class="rounded bg-sky-500/100 px-2 py-1 text-white">
          <option>功能选择</option>
        </select>
        <!-- <button class="text-xl text-orange-400">×</button> -->
      </div>

      <!-- 灯光雨刷操作 -->
      <div class="grid grid-cols-3 gap-2">
        <n-button type="info"> 刷雨刮 </n-button>
        <n-button type="info"> 开补光灯 </n-button>
        <n-button type="info"> 关补光灯 </n-button>
      </div>

      <!-- 水平/垂直控制 -->
      <div class="rounded p-3 pl-0">
        <div class="grid grid-cols-3">
          <div class="flex text-lg font-bold">
            水平 ( J <MoveHorizontalIcon /> L )
            <n-input-number v-model:value="focalLength" button-placement="both" size="small" style="width: 110px" class="ml-2" />
          </div>
          <div class="flex text-lg font-bold">
            垂直 ( I <MoveHorizontalIcon /> K )
            <n-input-number v-model:value="focalLength" button-placement="both" size="small" style="width: 110px" class="ml-2" />
          </div>
          <!-- 操作按钮 -->
          <div class="flex grid grid-cols-3 gap-2 text-lg font-bold">
            <n-button strong secondary type="info" class="rounded px-4 py-1"> 复位 </n-button>
            <n-button strong secondary type="info" class="rounded px-4 py-1"> 开始录像 </n-button>
            <n-button strong secondary type="info" class="rounded px-4 py-1"> 结束录像 </n-button>
          </div>
        </div>
      </div>

      <!-- 方向键 -->
      <div class="mt-4 flex items-center justify-center gap-4">
        <div class="grid grid-cols-3 gap-1">
          <div></div>
          <button class="h-10 w-10 rounded bg-red-600">W</button>
          <div></div>

          <button class="h-10 w-10 rounded bg-red-600">A</button>
          <div class="h-10 w-10 bg-transparent"></div>
          <button class="h-10 w-10 rounded bg-red-600">D</button>

          <div></div>
          <button class="h-10 w-10 rounded bg-red-600">S</button>
          <div></div>
        </div>

        <!-- 滑动条 -->
        <div class="flex flex-col items-center justify-center space-y-1">
          <button class="h-6 w-6 rounded-full bg-orange-500">+</button>
          <div class="h-20 w-1 rounded-full bg-orange-300"></div>
          <button class="h-6 w-6 rounded-full bg-orange-500">-</button>
        </div>
      </div>

      <!-- 底部功能 -->
      <div class="mt-4 flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <span class="text-lg font-bold">车体速(m/s)</span>
          <input type="text" value="0.3" class="w-12 rounded bg-sky-500/100 px-1 py-0.5 text-center text-white" />
        </div>
        <div class="flex gap-2">
          <button class="rounded bg-gray-700 px-4 py-1">停止踏步</button>
          <button class="rounded bg-gray-700 px-4 py-1">趴下模式</button>
        </div>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { MoveRightIcon, MoveHorizontalIcon } from "lucide-vue-next";

const showModal = ref(false);
const focalLength = ref(1);

defineExpose({
  open() {
    showModal.value = true;
  }
});
</script>

<style scoped>
:deep(.n-input.n-input--resizable.n-input--stateful) {
  /* --n-color: #364153 !important; */
  /* --n-border-hover: 1px solid #ff6900 !important; */
  --n-text-color: #0e8bff !important;
}
:deep(.n-button.n-button--default-type.n-button--medium-type) {
  --n-text-color: #ff761a !important;
}
</style>
