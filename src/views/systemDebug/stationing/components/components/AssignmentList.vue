<!-- 任务信息-列表 -->
<template>
  <n-modal v-model:show="showModal" preset="card" title="任务信息" style="width: 60%">
    <n-tabs type="line" animated>
      <n-tab-pane name="可执行任务" tab="可执行任务" style="height: 550px">
        <!-- 可执行任务-列表 -->
        <n-data-table
          :columns="columns"
          :data="tableData"
          :bordered="false"
          flex-height
          :style="{ height: '100%' }"
          :pagination="{ page: 1 }"
        ></n-data-table>
      </n-tab-pane>
      <n-tab-pane name="已完成任务" tab="已完成任务" style="height: 550px">
        <n-form ref="formRef" label-placement="left" inline :label-width="80" :model="formValue" size="small">
          <n-form-item label="日期：" path="time">
            <n-date-picker v-model:value="formValue.time" type="datetimerange" clearable />
          </n-form-item>
          <n-form-item>
            <n-button type="info" @click="handleValidateClick" size="small">搜索</n-button>
          </n-form-item>
        </n-form>
        <!-- 已完成任务-列表 -->
        <n-data-table
          :columns="doneColumns"
          :data="doneList"
          :bordered="false"
          flex-height
          :style="{ height: '100%' }"
          :pagination="{ page: 1 }"
        ></n-data-table>
      </n-tab-pane>
    </n-tabs>
  </n-modal>
</template>

<script setup lang="ts">
import { h, onMounted, ref } from "vue";
import { type DataTableColumn, NButton } from "naive-ui";
import { TASK_EXECUTION_STATUS_MAP } from "@/dictionary";
// import { getDailyTaskListApi } from "@/api/modules/mock";
// import { useTable } from "@/hooks";

interface RowData {
  id: number;
  name: string;
  robot: string;
  startTime: string;
  endTime: string;
  status: number;
  total: number;
  doneTotal: number;
  warningTotal: number;
}
const showModal = ref(false);
const formValue = ref<any>({
  time: null
});

// const { getTableList, tableData } = useTable(getDailyTaskListApi, {}, true, undefined, undefined, params => {
//   params.status = params.status?.join(",");
//   return params;
// });

defineExpose({
  open() {
    showModal.value = true;
  }
});

// 可执行任务
const tableData: RowData[] = [
  {
    id: 1,
    name: "表计_以图搜图",
    robot: "N000001",
    startTime: "2025-2-21 11:30",
    endTime: "",
    status: 3, // 未开始
    total: 0,
    doneTotal: 1,
    warningTotal: 0.1
  },
  {
    id: 2,
    name: "机器人狗",
    robot: "N000001",
    startTime: "2025-2-21 15:50",
    endTime: "",
    status: 3, // 未开始
    total: 0,
    doneTotal: 1,
    warningTotal: 0.1
  },
  {
    id: 3,
    name: "表",
    robot: "N000001",
    startTime: "2025-2-24 14:50",
    endTime: "",
    status: 1, // 未开始
    total: 0,
    doneTotal: 3,
    warningTotal: 0.1
  },
  {
    id: 4,
    name: "门图表",
    robot: "N000001",
    startTime: "2025-2-26 15:40",
    endTime: "",
    status: 2, // 未开始
    total: 0,
    doneTotal: 3,
    warningTotal: 0.1
  },
  {
    id: 5,
    name: "灯",
    robot: "N000001",
    startTime: "2025-2-27 15:30",
    endTime: "",
    status: 2, // 未开始
    total: 0,
    doneTotal: 1,
    warningTotal: 0.1
  }
];

// 已完成任务
const doneList: any[] = [
  {
    id: 1,
    name: "表计_以图搜图",
    robot: "N000001",
    startTime: "2025-2-21 11:30",
    endTime: "",
    status: 3, // 未开始
    total: 0,
    doneTotal: 1,
    warningTotal: 0.1
  },
  {
    id: 2,
    name: "机器人狗",
    robot: "N000001",
    startTime: "2025-2-21 15:50",
    endTime: "",
    status: 3, // 未开始
    total: 0,
    doneTotal: 1,
    warningTotal: 0.1
  }
];

const columns: DataTableColumn<RowData>[] = [
  {
    key: "name",
    title: "任务名称",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: "robot",
    title: "机器人",
    align: "center"
  },
  {
    key: "startTime",
    title: "开始时间",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: "endTime",
    title: "结束时间",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: "status",
    title: "任务状态",
    align: "center",
    render: rowData => TASK_EXECUTION_STATUS_MAP[rowData.status]
  },
  {
    key: "doneTotal",
    title: "优先级",
    align: "center"
  },
  {
    key: "warningTotal",
    title: "车载速度",
    align: "center"
  },
  {
    key: "action",
    title: "操作",
    render() {
      return h(NButton, { type: "warning", ghost: true }, { default: () => "立即导出" });
    }
  }
];

// 已完成任务
const doneColumns: DataTableColumn<RowData>[] = [
  {
    key: "name",
    title: "任务名称",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: "robot",
    title: "机器人",
    align: "center"
  },
  {
    key: "startTime",
    title: "开始时间",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: "endTime",
    title: "结束时间",
    align: "center",
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: "status",
    title: "任务状态",
    align: "center",
    render: rowData => TASK_EXECUTION_STATUS_MAP[rowData.status]
  },
  {
    key: "doneTotal",
    title: "任务总数点",
    align: "center"
  },
  {
    key: "doneTotal",
    title: "完成总数点",
    align: "center"
  },
  {
    key: "doneTotal",
    title: "警告总数",
    align: "center"
  },
  {
    key: "action",
    title: "操作",
    render() {
      return h(NButton, { type: "warning", ghost: true }, { default: () => "立即导出" });
    }
  }
];

// 搜索
const handleValidateClick = () => {};

onMounted(() => {
  // getTableList();
});
</script>

<style scoped>
:deep(.n-data-table .n-data-table-th) {
  background-color: rgb(150 200 255);
  color: #2c2c2c;
}
:deep(.n-data-table .n-data-table-td) {
  color: #3f3f3f;
}
</style>
