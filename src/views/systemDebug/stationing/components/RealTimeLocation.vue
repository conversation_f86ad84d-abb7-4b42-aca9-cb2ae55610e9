<!-- 实时位置 -->
<template>
  <div class="flex flex-col rounded-lg border border-primary-1 bg-[var(--card-visual-bg)] p-2">
    <div class="mb-2 flex items-center justify-between">
      <p class="flex items-center space-x-1">
        <LocateIcon class="size-4" />
        <span>实时位置</span>
      </p>
      <div class="flex space-x-2">
        <MaximizeIcon class="size-4" />
      </div>
    </div>
    <div class="flex-1 overflow-hidden">
      <img class="h-full w-full object-cover" src="https://picsum.photos/1038/460" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { LocateIcon, MaximizeIcon } from "lucide-vue-next";
</script>
