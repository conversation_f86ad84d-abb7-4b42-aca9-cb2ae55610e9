<!-- 巡检信息 -->
<template>
  <div class="flex w-full flex-wrap items-center gap-x-6 gap-y-2">
    <!-- ① 信息栏 -->
    <div
      class="relative flex w-[52%] flex-wrap items-center gap-x-6 gap-y-2 rounded border border-blue-400/70 bg-blue-600/30 px-3 py-1.5"
    >
      <!-- 机器人编号 -->
      <span class="flex items-center gap-1">
        <BotIcon class="h-4 w-4 shrink-0" />
        <span>机器人：</span>
        <span class="cursor-pointer rounded bg-blue-600/70 px-3 py-0.5 font-mono" @click="functionalClick">N000001</span>
        <div
          v-if="isFunctiona"
          class="absolute top-full left-0 z-50 mt-2 w-full rounded bg-blue-900 px-4 py-3 text-white shadow-xl"
        >
          <div class="flex flex-wrap items-center gap-2">
            <span>功能阵列：</span>
            <n-button :bordered="false" :focusable="false" color="#1a6affd6"> 重启机器人 </n-button>
            <n-button :bordered="false" :focusable="false" color="#1a6affd6"> 刷新数据 </n-button>
            <n-button :bordered="false" :focusable="false" color="#1a6affd6"> 重启云台 </n-button>
            <n-button :bordered="false" :focusable="false" color="#1a6affd6"> 关闭云台 </n-button>
            <n-button :bordered="false" :focusable="false" color="#1a6affd6"> 开启导航 </n-button>
            <n-button :bordered="false" :focusable="false" color="#1a6affd6"> 关闭导航 </n-button>
            <n-button :bordered="false" :focusable="false" color="#1a6affd6"> 切换地图 </n-button>
            <n-button :bordered="false" :focusable="false" color="#1a6affd6"> 导航关机 </n-button>
          </div>
        </div>
      </span>

      <!-- 状态 -->
      <span class="flex items-center gap-1">
        <AlertTriangleIcon class="h-4 w-4 shrink-0 text-red-400" />
        <span>状态：</span>
        <span class="text-orange-300">危急告警</span>
      </span>

      <!-- 模式 -->
      <span class="flex items-center gap-1">
        <PlayCircleIcon class="h-4 w-4 shrink-0" />
        <span>模式：</span>
        <span @click="patternClick" v-if="!isPattern" class="cursor-pointer rounded bg-blue-600/70 px-3 py-0.5">运行模式</span>
        <span @click="patternClick" v-if="isPattern" class="cursor-pointer rounded bg-blue-600/70 px-3 py-0.5">调试模式</span>
      </span>

      <!-- 任务 -->
      <span class="flex items-center gap-1">
        <ClipboardIcon class="h-4 w-4 shrink-0" />
        <span>任务：</span>
        <span>空闲</span>
      </span>

      <!-- 控制 -->
      <span class="flex items-center gap-1">
        <CogIcon class="h-4 w-4 shrink-0" />
        <span>控制：</span>
        <span class="flex items-center gap-3">
          <n-radio-group v-model:value="checkedValue" name="radiogroup">
            <n-space>
              <n-radio value="1" name="巡检" @click="openClick"> 巡检 </n-radio>
              <n-radio value="2" name="挂起/手控"> 挂起/手控 </n-radio>
              <n-radio value="3" name="返航"> 返航 </n-radio>
            </n-space>
          </n-radio-group>
        </span>
      </span>
    </div>

    <!-- ② 状态栏 -->
    <div
      class="flex w-[46.5%] flex-wrap items-center gap-x-6 gap-y-2 rounded border border-blue-400/70 bg-blue-600/30 px-3 py-1.5"
    >
      <span>状态：</span>
      <n-tooltip placement="bottom" trigger="hover">
        <template #trigger>
          <ServerCrashIcon class="h-5 w-5 shrink-0" />
        </template>
        <span> 总状态：[危急告警] </span>
      </n-tooltip>
      <n-tooltip placement="bottom" trigger="hover">
        <template #trigger>
          <MountainIcon class="h-5 w-5 shrink-0" />
        </template>
        <span> 路障：[正常] </span>
      </n-tooltip>
      <n-tooltip placement="bottom" trigger="hover">
        <template #trigger>
          <BatteryPlusIcon class="h-5 w-5 shrink-0" />
        </template>
        <span> 电池单体：[正常] </span>
      </n-tooltip>
      <n-tooltip placement="bottom" trigger="hover">
        <template #trigger>
          <ThermometerIcon class="h-5 w-5 shrink-0 text-orange-400" />
        </template>
        <span> 电池温度：[正常] </span>
      </n-tooltip>
      <n-tooltip placement="bottom" trigger="hover">
        <template #trigger>
          <BatteryFullIcon class="h-5 w-5 shrink-0 text-lime-300" />
        </template>
        <span>总电量：[正常] </span>
      </n-tooltip>
      <n-tooltip placement="bottom" trigger="hover">
        <template #trigger>
          <SignalHighIcon class="h-5 w-5 shrink-0" />
        </template>
        <span>云台通信：[正常] </span>
      </n-tooltip>
      <n-tooltip placement="bottom" trigger="hover">
        <template #trigger>
          <SmartphoneNfcIcon class="h-5 w-5 shrink-0" />
        </template>
        <span>主控通讯：[正常] </span>
      </n-tooltip>
      <n-tooltip placement="bottom" trigger="hover">
        <template #trigger>
          <NavigationIcon class="h-5 w-5 shrink-0" />
        </template>
        <span>导航通讯：[正常] </span>
      </n-tooltip>
      <n-tooltip placement="bottom" trigger="hover">
        <template #trigger>
          <HomeIcon class="h-5 w-5 shrink-0 text-orange-400" />
        </template>
        <span>充电房通讯：[正常] </span>
      </n-tooltip>

      <!-- 右侧“遥控” -->
      <span class="ml-auto flex items-center gap-1">
        遥控：
        <Gamepad2Icon class="h-5 w-5 shrink-0" @click="telecontrolClick" />
      </span>
    </div>

    <!-- 任务信息 -->
    <AssignmentList ref="assignmentListRef" />
    <!-- 操控中心 -->
    <Telecontrol ref="telecontrolRef" />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import AssignmentList from "./components/AssignmentList.vue";
import Telecontrol from "./components/Telecontrol.vue";
import {
  BotIcon,
  ClipboardIcon,
  AlertTriangleIcon,
  PlayCircleIcon,
  BatteryPlusIcon,
  CogIcon,
  MountainIcon,
  BatteryFullIcon,
  ThermometerIcon,
  SignalHighIcon,
  SmartphoneNfcIcon,
  NavigationIcon,
  HomeIcon,
  Gamepad2Icon,
  ServerCrashIcon
} from "lucide-vue-next";

// import { defineComponent, defineProps } from "vue";
const assignmentListRef = ref<InstanceType<typeof AssignmentList>>();
const telecontrolRef = ref<InstanceType<typeof Telecontrol>>();
const checkedValue = ref<string | null>(null);
const isFunctiona = ref(false);
const isPattern = ref(false);

// 功能阵列
const functionalClick = () => {
  isFunctiona.value = !isFunctiona.value;
};

// 模式调整
const patternClick = () => {
  isPattern.value = !isPattern.value;
};

// 巡检列表
const openClick = () => {
  assignmentListRef.value?.open();
};

// 遥控弹框
const telecontrolClick = () => {
  telecontrolRef.value?.open();
};
</script>
