<template>
  <div class="flex flex-col rounded-lg border border-primary-1 bg-[var(--card-visual-bg)] p-2">
    <div class="mb-2 flex items-center justify-between">
      <p class="flex items-center space-x-1">
        <CctvIcon class="size-4" />
        <span>{{ title }}</span>
      </p>
      <div class="flex space-x-2">
        <ArrowLeftRightIcon class="size-4" />
        <SlidersVerticalIcon class="size-4" />
      </div>
    </div>

    <div class="flex-1 overflow-hidden">
      <!-- <img class="h-full w-full object-cover" :src="url" /> -->
      <JessibucaPlayer
        class="h-full w-full object-cover"
        ref="jessibucaPlayerRef"
        ws-raw="ws://*************:18181/tv"
        :config="jessibucaPlayerConfig"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { CctvIcon, ArrowLeftRightIcon, SlidersVerticalIcon } from "lucide-vue-next";
import JessibucaPlayer from "@/components/JessibucaPlayer/index.vue";

interface Props {
  title: string;
  url?: string;
}
const jessibucaPlayerRef = ref<InstanceType<typeof JessibucaPlayer>>();

const jessibucaPlayerConfig: Partial<Jessibuca.Config> = {
  isFlv: true,
  showBandwidth: true,
  hasAudio: false,
  // useWCS: true,
  // useMSE: true,
  operateBtns: {
    screenshot: true,
    fullscreen: true,
    play: true,
    audio: true,
    record: true
  },
  loadingText: "加载中..."
};

withDefaults(defineProps<Props>(), {
  title: "",
  url: ""
});
</script>
