<!-- 系统调试-布点 -->
<template>
  <NConfigProvider class="h-full" :theme-overrides="themeOverrides">
    <main class="flex h-full flex-col bg-[#484848] text-white">
      <div class="over scrollable flex h-full flex-col gap-2 p-4">
        <!-- 数据 -->
        <div class="flex h-[5%]">
          <InspectionInfo class="h-full" />
        </div>
        <!-- 上部分 -->
        <div class="flex h-[61%] gap-2">
          <Camera class="flex-1" title="可见光影视区" />
          <Camera class="flex-1" title="红外线影视区" />
        </div>

        <!-- 下部分 -->
        <div class="grid h-[31%] grid-cols-4 gap-2">
          <RealTimeLocation class="h-full" />
          <PositionManage class="h-full" />
          <OverallStatus class="h-full" />
          <ObservationManage class="h-full" />
        </div>
      </div>
    </main>
  </NConfigProvider>
</template>

<script setup lang="ts">
import { NConfigProvider, type GlobalThemeOverrides } from "naive-ui";

// import Header from "@/views/home/<USER>/Header.vue";
import Camera from "./components/Camera.vue";
import OverallStatus from "./components/OverallStatus.vue";
import RealTimeLocation from "./components/RealTimeLocation.vue";
import PositionManage from "./components/PositionManage.vue";
import InspectionInfo from "./components/InspectionInfo.vue";
import ObservationManage from "./components/ObservationManage.vue";
// import TaskInfo from "@/views/home/<USER>/TaskInfo.vue";
// import ConsoleLog from "@/views/home/<USER>/ConsoleLog.vue";

const themeOverrides: GlobalThemeOverrides = {
  Tabs: {
    tabTextColorBar: "#fff",
    tabTextColorHoverBar: "var(--color-primary-2)",
    tabTextColorActiveBar: "var(--color-primary-2)",
    barColor: "var(--color-primary-2)"
  },
  Switch: {
    buttonColor: "#fff",
    railColorActive: "var(--color-primary-2)",
    railColor: "#E2E8F0"
  },
  DataTable: {
    thColor: "var(--color-primary-1)",
    thTextColor: "#fff",
    tdColor: "transparent",
    tdTextColor: "#fff",
    tdColorHover: "transparent",
    borderColor: "transparent"
  },
  Pagination: {
    buttonColor: "transparent",
    buttonBorder: "none",
    buttonBorderHover: "none",
    itemTextColorHover: "var(--color-primary-1)",
    itemTextColor: "#fff",
    itemColorActive: "transparent",
    itemBorderActive: "1px solid var(--color-primary-1)",
    itemTextColorActive: "var(--color-primary-1)",
    itemBorderDisabled: "none",
    itemTextColorDisabled: "var(--color-primary-1)",
    itemColorDisabled: "transparent",
    buttonBorderPressed: "transparent",
    buttonIconColor: "var(--color-primary-1)",
    buttonIconColorHover: "var(--color-primary-1)"
  },
  Button: {
    textColor: "#fff",
    border: "1px solid var(--color-primary-1)",
    borderHover: "1px solid var(--color-primary-1)",
    textColorHover: "var(--color-primary-2)",
    textColorPressed: "var(--color-primary-2)",
    textColorFocus: "var(--color-primary-2)",
    borderPressed: "1px solid var(--color-primary-1)",
    borderFocus: "var(--color-primary-1)"
  },
  Radio: {
    textColor: "#fff"
  }
};
</script>
