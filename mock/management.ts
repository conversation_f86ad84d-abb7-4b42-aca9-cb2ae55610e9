import { MockMethod } from "vite-plugin-mock";
import <PERSON><PERSON> from "mockjs";

export default [
  /** 巡检任务列表 */
  {
    url: "/mock/intelligence/task",
    method: "get",
    response: ({ query }) => {
      const page = parseInt(query.page || 1);
      const limit = parseInt(query.limit || 10);

      const total = 1000;

      return {
        data: {
          code: 0,
          msg: "成功",
          records: Mock.mock({
            [`list|${limit}`]: [
              {
                id: "@id",
                name: "ALL_111_以和机器人_2025030101632_例行巡检",
                robot: "N000001",
                createTime: "@date",
                type: "@pick(1,2)",
                validity: "@pick(0,1)",
                level: "@pick(1,2,3)",
                speed: 0.1
              }
            ]
          }).list,
          total,
          page,
          limit
        }
      };
    }
  },
  /** 巡检台账树 */
  {
    url: "/mock/intelligence/tree",
    method: "get",
    response: () => {
      return {
        code: 0,
        msg: "成功",
        data: [
          {
            key: "1",
            label: "以和机器人",
            children: [
              {
                key: "1-1",
                label: "测试",
                children: [
                  {
                    key: "1-1-1",
                    label: "A",
                    children: [
                      {
                        key: "1-1-1-1",
                        label: "A1设备",
                        children: [
                          {
                            key: "1-1-1-1-1",
                            label: "A1设备开关-压降1"
                          },
                          {
                            key: "1-1-1-1-2",
                            label: "A1设备开关-压降3"
                          },
                          {
                            key: "1-1-1-1-3",
                            label: "A1设备开关-圆形"
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      };
    }
  },
  /** 日常任务列表 */
  {
    url: "/mock/daily/task",
    method: "get",
    response: ({ query }) => {
      const page = parseInt(query.page || 1);
      const limit = parseInt(query.limit || 10);

      const total = 1000;

      return {
        data: {
          code: 0,
          msg: "成功",
          records: Mock.mock({
            [`list|${limit}`]: [
              {
                id: "@id",
                name: "111_111_以和机器人_20250218154123_例行巡检_@date('yyyy')@date('MM')@date('dd')@date('HH')@date('mm')@date('ss')",
                robot: "N000001",
                startTime: query.taskDate ? `${query.taskDate} 00:00:00` : "@datetime",
                endTime: query.taskDate ? `${query.taskDate} 23:59:59` : "@datetime",
                status: query.status ? `@pick(${query.status})` : "@pick(1,2,3,4,5,6)",
                total: 3,
                doneTotal: 3,
                warningTotal: 3
              }
            ]
          }).list,
          total,
          page,
          limit
        }
      };
    }
  }
] satisfies MockMethod[];
