import { <PERSON><PERSON><PERSON>eth<PERSON> } from "vite-plugin-mock";
import Mo<PERSON> from "mockjs";

export default [
  /** 任务信息 */
  {
    url: "/mock/visual/task-info",
    method: "get",
    response: () => {
      return {
        code: 0,
        msg: "成功",
        data: [
          {
            id: 1,
            title: "任务进度",
            value: "100%",
            color: "text-primary-4"
          },
          {
            id: 2,
            title: "巡检点总数",
            value: "4",
            color: "text-primary-2"
          },
          {
            id: 3,
            title: "完成",
            value: "4",
            color: "text-primary-3"
          },
          {
            id: 4,
            title: "告警",
            value: "0",
            color: "text-primary-2"
          },
          {
            id: 5,
            title: "失败",
            value: "0",
            color: "text-primary-5"
          },
          {
            id: 6,
            title: "当前位置点",
            value: "---",
            color: "text-primary-4"
          }
        ]
      };
    }
  },
  /** 任务信息列表 */
  {
    url: "/mock/visual/task-info-list",
    method: "get",
    response: ({ query }) => {
      const page = parseInt(query.page || 1);
      const limit = parseInt(query.limit || 10);

      const total = 1000;

      return {
        data: {
          code: 0,
          msg: "成功",
          records: Mock.mock({
            [`list|${limit}`]: [
              {
                id: "@id",
                name: "x02",
                speed: "@pick(0.1,0.2,0.3,0.4)",
                status: "@pick('完成', '失败')"
              }
            ]
          }).list,
          total,
          page,
          limit
        }
      };
    }
  },
  /** 巡检信息 */
  {
    url: "/mock/visual/inspection-info",
    method: "get",
    response: () => ({
      code: 0,
      msg: "成功",
      data: {
        inspection: { month: 200, day: 10 },
        alarm: { month: 10, day: 2 }
      }
    })
  },
  /** 机器人状态 */
  {
    url: "/mock/visual/robot-status",
    method: "get",
    response: ({ query }) => {
      const page = parseInt(query.page || 1);
      const limit = parseInt(query.limit || 10);

      const total = 1000;

      return {
        data: {
          code: 0,
          msg: "成功",
          records: Mock.mock({
            [`list|${limit}`]: [
              {
                id: "@id",
                module: "充电房",
                function: "功能",
                warning: "@pick(1,2,3)",
                error: "异常描述异常描述",
                createTime: "@date",
                checkTime: "@date",
                robot: "N000001",
                review: "@pick(1,2)"
              }
            ]
          }).list,
          total,
          page,
          limit
        }
      };
    }
  },
  /** 台账信息 */
  {
    url: "/mock/visual/ledger-list",
    method: "get",
    response: ({ query }) => {
      const page = parseInt(query.page || 1);
      const limit = parseInt(query.limit || 10);

      const total = 1000;

      return {
        data: {
          code: 0,
          msg: "成功",
          records: Mock.mock({
            [`list|${limit}`]: [
              {
                id: "@id",
                alarmLevel: "@pick(1,2)",
                region: "测试",
                device: "A1设备",
                inspectionPoint: "A1设备开",
                detectionType: "位置状态",
                value: 1,
                imageUrl: "@image('200x150')",
                time: "@date",
                executionStatus: "成功"
              }
            ]
          }).list,
          total,
          page,
          limit
        }
      };
    }
  },
  /** 机器人状态 */
  {
    url: "/mock/visual/overall-status",
    method: "get",
    response: ({ query }) => {
      console.log(query);

      return {
        code: 0,
        msg: "成功",
        data: [
          {
            id: 1,
            label: "机器人状态",
            icon: "BotIcon",
            status: "@pick(0,1,2,3,4,5)"
          },
          {
            id: 2,
            label: "路障",
            icon: "TrafficConeIcon",
            status: "@pick(0,1,2,3,4,5)"
          },
          {
            id: 3,
            label: "电池单体",
            icon: "BatteryIcon",
            status: "@pick(0,1,2,3,4,5)"
          },
          {
            id: 4,
            label: "电池温度",
            icon: "ThermometerIcon",
            status: "@pick(0,1,2,3,4,5)"
          },
          {
            id: 5,
            label: "总电量",
            icon: "BatteryFullIcon",
            status: "@pick(0,1,2,3,4,5)"
          },
          {
            id: 6,
            label: "箱温",
            icon: "BoxIcon",
            status: "@pick(0,1,2,3,4,5)"
          },
          {
            id: 7,
            label: "云台通讯",
            icon: "SignalIcon",
            status: "@pick(0,1,2,3,4,5)"
          },
          {
            id: 8,
            label: "主控通讯",
            icon: "RadioTowerIcon",
            status: "@pick(0,1,2,3,4,5)"
          },
          {
            id: 9,
            label: "导航通讯",
            icon: "Navigation2Icon",
            status: "@pick(0,1,2,3,4,5)"
          },
          {
            id: 10,
            label: "充电房通讯",
            icon: "HousePlugIcon",
            status: "@pick(0,1,2,3,4,5)"
          }
        ]
      };
    }
  }
] satisfies MockMethod[];
